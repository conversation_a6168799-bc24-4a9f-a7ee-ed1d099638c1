from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class PatientInfo(BaseModel):
    """Patient information model."""
    name: str = Field(..., min_length=1, max_length=100, description="<PERSON><PERSON>'s full name")
    age: int = Field(..., ge=0, le=150, description="Patient's age")
    gender: str = Field(..., description="Patient's gender")
    mrn: Optional[str] = Field(None, max_length=50, description="Medical Record Number")
    reason_for_visit: str = Field(..., min_length=1, max_length=500, description="Reason for the visit")
    
    class Config:
        json_schema_extra = {
            "example": {
                "name": "<PERSON>",
                "age": 45,
                "gender": "Male",
                "mrn": "MRN123456",
                "reason_for_visit": "Annual checkup and blood pressure monitoring"
            }
        }


class PatientInfoResponse(PatientInfo):
    """Patient information response model."""
    created_at: datetime
    updated_at: Optional[datetime] = None
