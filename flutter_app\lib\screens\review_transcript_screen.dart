import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../providers/consultation_provider.dart';
import '../providers/transcript_provider.dart';
import '../models/transcript_segment.dart';
import '../utils/constants.dart';

class ReviewTranscriptScreen extends StatefulWidget {
  const ReviewTranscriptScreen({super.key});

  @override
  State<ReviewTranscriptScreen> createState() => _ReviewTranscriptScreenState();
}

class _ReviewTranscriptScreenState extends State<ReviewTranscriptScreen> {
  final _notesController = TextEditingController();
  bool _isGeneratingPdf = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Review Transcript'),
        actions: [
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: _generatePdf,
            tooltip: 'Generate PDF',
          ),
        ],
      ),
      body: Consumer2<ConsultationProvider, TranscriptProvider>(
        builder: (context, consultationProvider, transcriptProvider, child) {
          final consultation = consultationProvider.currentConsultation;
          final segments = transcriptProvider.transcriptSegments;

          if (consultation == null) {
            return const Center(
              child: Text('No consultation data available'),
            );
          }

          return Column(
            children: [
              // Patient Info Header
              Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        consultation.patientInfo.name,
                        style: AppTextStyles.headline3,
                      ),
                      Text(
                        'Duration: ${consultation.formattedDuration}',
                        style: AppTextStyles.bodyMedium,
                      ),
                      Text(
                        'Status: ${consultation.status.displayName}',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ),

              // Transcript List
              Expanded(
                child: segments.isEmpty
                    ? const Center(
                        child: Text('No transcript available'),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: segments.length,
                        itemBuilder: (context, index) {
                          final segment = segments[index];
                          return _buildTranscriptSegment(segment, index);
                        },
                      ),
              ),

              // Doctor Notes Section
              Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Doctor\'s Notes',
                        style: AppTextStyles.headline3,
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          hintText: 'Add your notes here...',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 4,
                        maxLength: AppConstants.maxNotesLength,
                      ),
                    ],
                  ),
                ),
              ),

              // Action Buttons
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isGeneratingPdf ? null : _generatePdf,
                        icon: _isGeneratingPdf
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.picture_as_pdf),
                        label: Text(_isGeneratingPdf ? 'Generating...' : 'Generate PDF'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      onPressed: () => context.go('/'),
                      icon: const Icon(Icons.home),
                      label: const Text('New Consultation'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.success,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTranscriptSegment(TranscriptSegment segment, int index) {
    Color speakerColor;
    IconData speakerIcon;

    switch (segment.speaker) {
      case SpeakerType.doctor:
        speakerColor = AppColors.doctorColor;
        speakerIcon = Icons.medical_services;
        break;
      case SpeakerType.patient:
        speakerColor = AppColors.patientColor;
        speakerIcon = Icons.person;
        break;
      default:
        speakerColor = AppColors.otherColor;
        speakerIcon = Icons.help_outline;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(speakerIcon, color: speakerColor, size: 16),
                const SizedBox(width: 8),
                Text(
                  segment.speaker.displayName,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: speakerColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  segment.formattedStartTime,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              segment.text,
              style: AppTextStyles.bodyMedium,
            ),
            if (segment.confidence != null)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Confidence: ${(segment.confidence! * 100).toStringAsFixed(1)}%',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _generatePdf() async {
    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      final consultationProvider = context.read<ConsultationProvider>();
      
      await consultationProvider.generatePdf(
        doctorNotes: _notesController.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('PDF generated successfully!'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to generate PDF: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingPdf = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }
}
