Table of Contents
Project Overview

Folder Structure

Prerequisites

Flutter App Setup

Configuration

Installing Dependencies

Running on Device/Emulator

Python Back-End Setup

Configuration

Installing Dependencies

Running with Uvicorn

Environment Variables & Secrets

Usage Workflow

Testing & Validation

License

Project Overview
DocTranscribe is a cross-platform mobile solution that lets a doctor:

Enter patient metadata (name, age, gender, MRN, reason for visit).

Record a doctor–patient conversation via a one‐tap interface.

Stream audio to an NVIDIA Riva ASR model (Parakeet-1.1B RNNT) for real-time transcription.

Filter out any third-party voices via speaker‐diarization or voice-ID.

Review and optionally edit a cleaned, timestamped transcript.

Generate a structured PDF report (cover page, transcript pages, optional notes) and save/share it.

The Flutter app handles UI, audio capture, and (optionally) client-side PDF generation. The Python back end (FastAPI) manages:

gRPC communication with NVIDIA Riva.

Speaker‐filtering/diarization.

(Optional) Server-side PDF generation.

Secure storage of audio/transcripts (encrypted at rest).

Folder Structure
pgsql
Copy
Edit
project_root/
├── flutter_app/
│   ├── android/
│   ├── ios/
│   ├── lib/
│   │   ├── main.dart
│   │   ├── screens/
│   │   │   ├── patient_info_screen.dart
│   │   │   ├── recording_screen.dart
│   │   │   ├── review_transcript_screen.dart
│   │   │   ├── history_screen.dart
│   │   │   └── settings_screen.dart
│   │   ├── widgets/
│   │   │   ├── audio_level_meter.dart
│   │   │   ├── live_transcript_preview.dart
│   │   │   └── transcript_line_item.dart
│   │   ├── services/
│   │   │   ├── api_service.dart
│   │   │   ├── audio_service.dart
│   │   │   ├── transcript_service.dart
│   │   │   ├── pdf_service.dart
│   │   │   └── storage_service.dart
│   │   ├── models/
│   │   │   ├── consultation.dart
│   │   │   ├── transcript_segment.dart
│   │   │   └── patient_info.dart
│   │   ├── providers/
│   │   │   ├── consultation_provider.dart
│   │   │   └── transcript_provider.dart
│   │   └── utils/
│   │       ├── constants.dart
│   │       └── helpers.dart
│   ├── assets/
│   ├── pubspec.yaml
│   └── README.md
│
└── backend/
    ├── app/
    │   ├── main.py
    │   ├── api/
    │   │   ├── routers/
    │   │   │   ├── consultation_router.py
    │   │   │   ├── audio_router.py
    │   │   │   ├── transcript_router.py
    │   │   │   └── pdf_router.py
    │   │   ├── dependencies.py
    │   │   └── schemas.py
    │   ├── core/
    │   │   ├── config.py
    │   │   ├── security.py
    │   │   └── utils.py
    │   ├── services/
    │   │   ├── riva_client.py
    │   │   ├── diarization_service.py
    │   │   ├── transcript_processing.py
    │   │   └── pdf_generator.py
    │   ├── models/
    │   │   ├── consultation.py
    │   │   ├── transcript_segment.py
    │   │   └── patient.py
    │   ├── db/
    │   │   ├── database.py
    │   │   └── crud.py
    │   └── tests/
    │       ├── test_api.py
    │       ├── test_diarization.py
    │       └── test_pdf_generation.py
    ├── requirements.txt
    ├── Dockerfile
    └── README.md
flutter_app/: Flutter project

lib/screens/: UI screens (patient info, recording, review, history, settings)

lib/widgets/: Reusable UI components

lib/services/: Business logic (API calls, audio capture, transcript handling, PDF building, local storage)

lib/models/: Dart data models

lib/providers/: State management (Provider/Riverpod)

lib/utils/: Constants and helper functions

assets/: Fonts, images, icons

backend/: Python/FastAPI service

app/api/routers/: Endpoint definitions (/start_consultation, /audio_chunk, /finish_consultation, /generate_pdf)

app/core/: Configuration, security utilities

app/services/: AI logic (Riva gRPC client, diarization, transcript processing, PDF generation)

app/models/: Pydantic/SQLAlchemy models

app/db/: Database (SQLite) setup and CRUD operations

app/tests/: Unit and integration tests

Prerequisites
Flutter App

Flutter SDK (3.x or above)

Xcode (for iOS) or Android Studio (with Android SDK)

A valid NVIDIA Riva API key (for development)

Device/emulator with microphone permissions

Python Back-End

Python 3.8+

Virtual environment (venv or conda recommended)

NVIDIA Riva Python client (nvidia-riva-client)

pyannote-audio (for speaker diarization)

ReportLab or FPDF (if server-side PDF)

FastAPI & Uvicorn

Flutter App Setup
Navigate to the Flutter folder

bash
Copy
Edit
cd project_root/flutter_app
Configuration

Open lib/utils/constants.dart and set:

API_BASE_URL to your Python back-end’s base URL (e.g., https://api.example.com).

(Optional) In lib/widgets/live_transcript_preview.dart, set default language codes if desired.

Install Dependencies

bash
Copy
Edit
flutter pub get
Run on Device or Emulator

Android:

bash
Copy
Edit
flutter run
iOS:

bash
Copy
Edit
flutter run
Ensure microphone permissions are granted when prompted.

Python Back-End Setup
Navigate to the Backend folder

bash
Copy
Edit
cd project_root/backend
Create a Virtual Environment & Activate

bash
Copy
Edit
python3 -m venv venv
source venv/bin/activate
Install Dependencies

bash
Copy
Edit
pip install -r requirements.txt
Configuration

Copy .env.example (if provided) to .env and set:

ini
Copy
Edit
RIVA_API_KEY=<your_nvidia_riva_api_key>
RIVA_FUNCTION_ID=<parakeet-1.1b-rnnt-function-id>
JWT_SECRET=<your_jwt_secret_or_api_key>
In app/core/config.py, ensure environment variables map correctly (API URLs, local storage paths).

Run with Uvicorn

bash
Copy
Edit
cd app
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
The API will be available at http://localhost:8000.

Verify the /docs endpoint (Swagger UI) at http://localhost:8000/docs.

Environment Variables & Secrets
Both Flutter and Python services require your NVIDIA Riva credentials:

RIVA_API_KEY: Bearer token for Riva authentication

RIVA_FUNCTION_ID: UUID of the Parakeet-1.1B RNNT model

JWT_SECRET (optional): For securing API endpoints (if using JWT)

Store keys securely (e.g., in .env for Python, in a private config file or CI/CD secrets for Flutter). Do not commit credentials to version control.

Usage Workflow
Doctor logs into the Flutter app (if authentication is enabled) or starts in “Guest Mode.”

Patient Info Screen: Enter name, age, gender, MRN, reason for visit. Tap “Next.”

Flutter calls POST /start_consultation, receives a consultation_id.

Recording Screen:

Tap “Record” to begin capturing audio.

Flutter streams 100 ms chunks to POST /audio_chunk with header X-Consultation-ID.

Backend forwards chunks to NVIDIA Riva; returns partial/final transcripts. Flutter displays them live.

Tap “Stop” to end.

Finish Consultation: Flutter calls POST /finish_consultation.

Backend closes the gRPC stream, runs speaker-diarization (pyannote), filters out third-party voices, and returns a cleaned list of {timestamp, speaker, text}.

Review Transcript Screen: Flutter displays each line with timestamps and speaker labels.

Doctor can edit any line or add “Impression & Plan” notes.

Tap “Generate PDF.”

PDF Generation & Save:

Client-side: Flutter’s pdf_service.dart builds and saves a PDF under Documents/DocTranscribe; shows a share sheet.

Server-side: Flutter calls POST /generate_pdf with doctor’s notes; backend uses pdf_generator.py to assemble a PDF and returns raw bytes or a URL. Flutter saves/shares.

History Screen: Shows a list of past consultations (patient, date, duration). Tapping an entry opens the saved PDF.

Settings Screen:

Manage NVIDIA Riva API key, default language, speaker-filter mode (real-time vs. post-processing), backend URL.

Toggle offline mode.

Testing & Validation
Flutter Unit & Widget Tests:

Verify form validation on Patient Info Screen.

Mock API responses for /audio_chunk and /finish_consultation to test live transcript preview and review screen.

Python Back-End Tests (in app/tests/):

test_api.py: Ensure each endpoint responds correctly (happy and error paths).

test_diarization.py: Feed a known multi-speaker WAV to diarization_service.py and verify expected clusters.

test_pdf_generation.py: Generate a PDF with sample data; confirm that cover page and transcript pages match expected layout.

End-to-End Validation:

Deploy Python back end locally (uvicorn main:app).

Run Flutter on an emulator or device pointed at http://localhost:8000.

Record a 30-second role-play conversation (English, Spanish, or Hindi).

Verify:

Live transcripts appear with < 500 ms latency.

Speaker filtering removes background/third-party voices.

PDF is generated in < 5 s for a 2-minute recording.

PDF layout matches the specified template (cover page with metadata, timestamped transcript, notes).

