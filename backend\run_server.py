#!/usr/bin/env python3
"""
Simple script to run the DocTranscribe backend server
"""

import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Now import and run the app
if __name__ == "__main__":
    import uvicorn
    from app.main import app
    
    print("🏥 Starting DocTranscribe Backend Server...")
    print(f"📁 Backend directory: {backend_dir}")
    print(f"🔧 Python path: {sys.path[0]}")
    print("🌐 Server will be available at: http://localhost:8000")
    print("📚 API documentation: http://localhost:8000/docs")
    print()
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
