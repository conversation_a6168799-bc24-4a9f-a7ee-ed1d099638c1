import os
import logging
from datetime import datetime
from typing import List, Optional
from pathlib import Path
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

from app.models.consultation import Consultation
from app.models.transcript_segment import TranscriptSegment
from app.core.config import settings
from app.core.utils import generate_filename, format_duration

logger = logging.getLogger(__name__)


class PDFGenerator:
    """Service for generating consultation PDF reports."""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Setup custom paragraph styles for the PDF."""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        ))
        
        # Header style
        self.styles.add(ParagraphStyle(
            name='CustomHeader',
            parent=self.styles['Heading1'],
            fontSize=14,
            spaceAfter=12,
            textColor=colors.darkblue
        ))
        
        # Subheader style
        self.styles.add(ParagraphStyle(
            name='CustomSubHeader',
            parent=self.styles['Heading2'],
            fontSize=12,
            spaceAfter=8,
            textColor=colors.darkgreen
        ))
        
        # Transcript style
        self.styles.add(ParagraphStyle(
            name='TranscriptText',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=6,
            leftIndent=20
        ))
        
        # Speaker label style
        self.styles.add(ParagraphStyle(
            name='SpeakerLabel',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        ))
    
    async def generate_consultation_pdf(
        self, 
        consultation: Consultation,
        transcript_segments: List[TranscriptSegment],
        doctor_notes: Optional[str] = None,
        output_dir: Optional[str] = None
    ) -> str:
        """
        Generate a comprehensive PDF report for a consultation.
        
        Args:
            consultation: Consultation object
            transcript_segments: List of transcript segments
            doctor_notes: Additional doctor's notes
            output_dir: Output directory (defaults to settings.pdf_output_dir)
            
        Returns:
            Path to generated PDF file
        """
        try:
            # Setup output directory and filename
            if output_dir is None:
                output_dir = settings.pdf_output_dir
            
            os.makedirs(output_dir, exist_ok=True)
            
            filename = generate_filename(
                f"consultation_{consultation.patient_info.name.replace(' ', '_')}", 
                "pdf"
            )
            pdf_path = os.path.join(output_dir, filename)
            
            # Create PDF document
            doc = SimpleDocTemplate(
                pdf_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Build PDF content
            story = []
            
            # Cover page
            story.extend(self._build_cover_page(consultation))
            story.append(PageBreak())
            
            # Transcript pages
            story.extend(self._build_transcript_pages(transcript_segments))
            
            # Doctor's notes page
            if doctor_notes or consultation.doctor_notes:
                story.append(PageBreak())
                story.extend(self._build_notes_page(doctor_notes or consultation.doctor_notes))
            
            # Build PDF
            doc.build(story)
            
            logger.info(f"PDF generated successfully: {pdf_path}")
            return pdf_path
            
        except Exception as e:
            logger.error(f"Error generating PDF: {e}")
            raise
    
    def _build_cover_page(self, consultation: Consultation) -> List:
        """Build the cover page content."""
        story = []
        
        # Title
        story.append(Paragraph("Medical Consultation Report", self.styles['CustomTitle']))
        story.append(Spacer(1, 30))
        
        # Patient Information Table
        patient_data = [
            ['Patient Information', ''],
            ['Name:', consultation.patient_info.name],
            ['Age:', str(consultation.patient_info.age)],
            ['Gender:', consultation.patient_info.gender],
            ['MRN:', consultation.patient_info.mrn or 'N/A'],
            ['Reason for Visit:', consultation.patient_info.reason_for_visit],
        ]
        
        patient_table = Table(patient_data, colWidths=[2*inch, 4*inch])
        patient_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (1, 0), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(patient_table)
        story.append(Spacer(1, 30))
        
        # Consultation Information Table
        consultation_data = [
            ['Consultation Information', ''],
            ['Doctor:', consultation.doctor_name or 'N/A'],
            ['Date:', consultation.start_time.strftime('%Y-%m-%d') if consultation.start_time else 'N/A'],
            ['Start Time:', consultation.start_time.strftime('%H:%M:%S') if consultation.start_time else 'N/A'],
            ['Duration:', format_duration(consultation.duration) if consultation.duration else 'N/A'],
            ['Status:', consultation.status.value.title()],
        ]
        
        consultation_table = Table(consultation_data, colWidths=[2*inch, 4*inch])
        consultation_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (1, 0), colors.lightgreen),
            ('TEXTCOLOR', (0, 0), (1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(consultation_table)
        story.append(Spacer(1, 50))
        
        # Footer
        story.append(Paragraph(
            f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            self.styles['Normal']
        ))
        
        return story
    
    def _build_transcript_pages(self, transcript_segments: List[TranscriptSegment]) -> List:
        """Build the transcript pages content."""
        story = []
        
        # Transcript header
        story.append(Paragraph("Consultation Transcript", self.styles['CustomHeader']))
        story.append(Spacer(1, 20))
        
        if not transcript_segments:
            story.append(Paragraph("No transcript available.", self.styles['Normal']))
            return story
        
        # Group segments by speaker for better readability
        current_speaker = None
        current_text = ""
        current_start_time = None
        
        for segment in transcript_segments:
            if segment.speaker != current_speaker:
                # Add previous speaker's text if exists
                if current_speaker is not None and current_text:
                    story.extend(self._add_speaker_segment(
                        current_speaker, current_text, current_start_time
                    ))
                
                # Start new speaker segment
                current_speaker = segment.speaker
                current_text = segment.text
                current_start_time = segment.start_time
            else:
                # Continue with same speaker
                current_text += " " + segment.text
        
        # Add the last segment
        if current_speaker is not None and current_text:
            story.extend(self._add_speaker_segment(
                current_speaker, current_text, current_start_time
            ))
        
        return story
    
    def _add_speaker_segment(self, speaker, text, start_time) -> List:
        """Add a speaker segment to the story."""
        story = []
        
        # Speaker label with timestamp
        speaker_label = f"{speaker.value.title()} [{format_duration(start_time)}]:"
        story.append(Paragraph(speaker_label, self.styles['SpeakerLabel']))
        
        # Speaker text
        story.append(Paragraph(text, self.styles['TranscriptText']))
        story.append(Spacer(1, 10))
        
        return story
    
    def _build_notes_page(self, notes: str) -> List:
        """Build the doctor's notes page."""
        story = []
        
        # Notes header
        story.append(Paragraph("Doctor's Notes", self.styles['CustomHeader']))
        story.append(Spacer(1, 20))
        
        # Notes content
        if notes:
            # Split notes into paragraphs
            paragraphs = notes.split('\n')
            for paragraph in paragraphs:
                if paragraph.strip():
                    story.append(Paragraph(paragraph.strip(), self.styles['Normal']))
                    story.append(Spacer(1, 12))
        else:
            story.append(Paragraph("No additional notes provided.", self.styles['Normal']))
        
        return story


# Global PDF generator instance
pdf_generator = PDFGenerator()
