from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum
from .patient import PatientInfo
from .transcript_segment import TranscriptSegment


class ConsultationStatus(str, Enum):
    """Enumeration for consultation status."""
    CREATED = "created"
    RECORDING = "recording"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class Consultation(BaseModel):
    """Consultation model."""
    id: str = Field(..., description="Unique consultation identifier")
    patient_info: PatientInfo = Field(..., description="Patient information")
    status: ConsultationStatus = Field(default=ConsultationStatus.CREATED, description="Consultation status")
    doctor_name: Optional[str] = Field(None, description="Doctor's name")
    start_time: Optional[datetime] = Field(None, description="Recording start time")
    end_time: Optional[datetime] = Field(None, description="Recording end time")
    duration: Optional[float] = Field(None, ge=0, description="Duration in seconds")
    audio_file_path: Optional[str] = Field(None, description="Path to audio file")
    transcript_segments: List[TranscriptSegment] = Field(default=[], description="Transcript segments")
    doctor_notes: Optional[str] = Field(None, description="Doctor's additional notes")
    pdf_file_path: Optional[str] = Field(None, description="Path to generated PDF")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "patient_info": {
                    "name": "John Doe",
                    "age": 45,
                    "gender": "Male",
                    "mrn": "MRN123456",
                    "reason_for_visit": "Annual checkup"
                },
                "status": "completed",
                "doctor_name": "Dr. Smith",
                "start_time": "2024-01-15T10:00:00Z",
                "end_time": "2024-01-15T10:15:00Z",
                "duration": 900.0,
                "doctor_notes": "Patient appears healthy. Recommend follow-up in 6 months.",
                "created_at": "2024-01-15T10:00:00Z"
            }
        }


class ConsultationCreate(BaseModel):
    """Model for creating a new consultation."""
    patient_info: PatientInfo
    doctor_name: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "patient_info": {
                    "name": "John Doe",
                    "age": 45,
                    "gender": "Male",
                    "mrn": "MRN123456",
                    "reason_for_visit": "Annual checkup"
                },
                "doctor_name": "Dr. Smith"
            }
        }


class ConsultationUpdate(BaseModel):
    """Model for updating consultation."""
    status: Optional[ConsultationStatus] = None
    doctor_notes: Optional[str] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None


class ConsultationResponse(BaseModel):
    """Response model for consultation."""
    id: str
    patient_info: PatientInfo
    status: ConsultationStatus
    doctor_name: Optional[str]
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    duration: Optional[float]
    transcript_count: int = 0
    has_pdf: bool = False
    created_at: datetime
    updated_at: Optional[datetime]


class ConsultationListResponse(BaseModel):
    """Response model for consultation list."""
    consultations: List[ConsultationResponse]
    total: int
    page: int
    page_size: int
