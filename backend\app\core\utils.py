import os
import uuid
import logging
from datetime import datetime
from typing import Optional
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def generate_consultation_id() -> str:
    """Generate a unique consultation ID."""
    return str(uuid.uuid4())


def generate_filename(prefix: str, extension: str) -> str:
    """Generate a unique filename with timestamp."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]
    return f"{prefix}_{timestamp}_{unique_id}.{extension}"


def ensure_directory_exists(directory_path: str) -> None:
    """Ensure a directory exists, create if it doesn't."""
    Path(directory_path).mkdir(parents=True, exist_ok=True)


def cleanup_temp_files(directory: str, max_age_hours: int = 24) -> None:
    """Clean up temporary files older than max_age_hours."""
    try:
        current_time = datetime.now()
        for file_path in Path(directory).glob("*"):
            if file_path.is_file():
                file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_age.total_seconds() > max_age_hours * 3600:
                    file_path.unlink()
                    logger.info(f"Cleaned up old temp file: {file_path}")
    except Exception as e:
        logger.error(f"Error cleaning up temp files: {e}")


def format_duration(seconds: float) -> str:
    """Format duration in seconds to human-readable format."""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes:02d}:{seconds:02d}"


def validate_audio_format(file_path: str) -> bool:
    """Validate if the audio file format is supported."""
    supported_formats = ['.wav', '.mp3', '.m4a', '.flac', '.ogg']
    file_extension = Path(file_path).suffix.lower()
    return file_extension in supported_formats
