import 'package:flutter/material.dart';

/// Application constants and configuration
class AppConstants {
  // API Configuration
  static const String apiBaseUrl = 'http://localhost:8000/api/v1';
  static const String apiVersion = 'v1';
  
  // Audio Configuration
  static const int audioSampleRate = 16000;
  static const int audioChannels = 1;
  static const int audioChunkDurationMs = 100;
  static const int maxAudioChunkSize = 1048576; // 1MB
  
  // Recording Configuration
  static const Duration maxRecordingDuration = Duration(hours: 2);
  static const Duration minRecordingDuration = Duration(seconds: 5);
  
  // PDF Configuration
  static const String pdfOutputDir = 'Documents/DocTranscribe';
  
  // Supported Languages
  static const Map<String, String> supportedLanguages = {
    'en-US': 'English (US)',
    'es-ES': 'Spanish (Spain)',
    'hi-IN': 'Hindi (India)',
    'fr-FR': 'French (France)',
    'de-DE': 'German (Germany)',
  };
  
  // Default Values
  static const String defaultLanguage = 'en-US';
  static const String defaultDoctorName = 'Dr. Unknown';
  
  // Validation
  static const int maxPatientNameLength = 100;
  static const int maxReasonLength = 500;
  static const int maxNotesLength = 2000;
  static const int minAge = 0;
  static const int maxAge = 150;
  
  // UI Configuration
  static const double defaultPadding = 16.0;
  static const double defaultBorderRadius = 8.0;
  static const Duration animationDuration = Duration(milliseconds: 300);
  
  // Storage Keys
  static const String storageKeySettings = 'app_settings';
  static const String storageKeyHistory = 'consultation_history';
  static const String storageKeyDoctorName = 'doctor_name';
  static const String storageKeyLanguage = 'selected_language';
}

/// Application color scheme
class AppColors {
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryDark = Color(0xFF1976D2);
  static const Color secondary = Color(0xFF4CAF50);
  static const Color accent = Color(0xFFFF9800);
  
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Colors.white;
  static const Color error = Color(0xFFE53935);
  static const Color warning = Color(0xFFFF9800);
  static const Color success = Color(0xFF4CAF50);
  
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  
  static const Color divider = Color(0xFFE0E0E0);
  static const Color border = Color(0xFFE0E0E0);
  
  // Recording specific colors
  static const Color recordingActive = Color(0xFFE53935);
  static const Color recordingInactive = Color(0xFF757575);
  static const Color audioLevel = Color(0xFF4CAF50);
  
  // Speaker colors
  static const Color doctorColor = Color(0xFF2196F3);
  static const Color patientColor = Color(0xFF4CAF50);
  static const Color otherColor = Color(0xFF757575);
}

/// Text styles
class AppTextStyles {
  static const TextStyle headline1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle headline2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle headline3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    color: AppColors.textSecondary,
  );
  
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    color: AppColors.textHint,
  );
  
  static const TextStyle button = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: Colors.white,
  );
}

/// Animation configurations
class AppAnimations {
  static const Duration fast = Duration(milliseconds: 150);
  static const Duration normal = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve bounceCurve = Curves.elasticOut;
}

/// Error messages
class ErrorMessages {
  static const String networkError = 'Network connection error. Please check your internet connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String audioPermissionDenied = 'Microphone permission is required to record audio.';
  static const String audioRecordingFailed = 'Failed to start audio recording.';
  static const String transcriptionFailed = 'Failed to transcribe audio. Please try again.';
  static const String pdfGenerationFailed = 'Failed to generate PDF report.';
  static const String invalidPatientInfo = 'Please fill in all required patient information.';
  static const String recordingTooShort = 'Recording is too short. Minimum duration is 5 seconds.';
  static const String recordingTooLong = 'Recording is too long. Maximum duration is 2 hours.';
  static const String fileNotFound = 'File not found.';
  static const String storagePermissionDenied = 'Storage permission is required to save files.';
}

/// Success messages
class SuccessMessages {
  static const String consultationStarted = 'Consultation started successfully.';
  static const String consultationFinished = 'Consultation finished successfully.';
  static const String pdfGenerated = 'PDF report generated successfully.';
  static const String pdfSaved = 'PDF saved to device.';
  static const String settingsSaved = 'Settings saved successfully.';
  static const String transcriptUpdated = 'Transcript updated successfully.';
}

/// Validation patterns
class ValidationPatterns {
  static final RegExp namePattern = RegExp(r'^[a-zA-Z\s]+$');
  static final RegExp mrnPattern = RegExp(r'^[a-zA-Z0-9]+$');
  static final RegExp phonePattern = RegExp(r'^\+?[\d\s\-\(\)]+$');
  static final RegExp emailPattern = RegExp(r'^[\w\.-]+@[\w\.-]+\.\w+$');
}
