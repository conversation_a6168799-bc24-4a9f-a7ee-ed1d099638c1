import logging
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from ..models.transcript_segment import TranscriptSegment, SpeakerType

logger = logging.getLogger(__name__)


class TranscriptProcessor:
    """Service for processing and cleaning transcripts."""
    
    def __init__(self):
        self.medical_terms = self._load_medical_terms()
        self.common_corrections = self._load_common_corrections()
    
    def _load_medical_terms(self) -> Dict[str, str]:
        """Load medical terminology corrections."""
        return {
            # Common medical term corrections
            "blood pressure": "blood pressure",
            "heart rate": "heart rate",
            "temperature": "temperature",
            "prescription": "prescription",
            "medication": "medication",
            "symptoms": "symptoms",
            "diagnosis": "diagnosis",
            "treatment": "treatment",
            "follow up": "follow-up",
            "followup": "follow-up",
            # Add more medical terms as needed
        }
    
    def _load_common_corrections(self) -> Dict[str, str]:
        """Load common transcription corrections."""
        return {
            # Common ASR errors
            "um": "",
            "uh": "",
            "ah": "",
            "  ": " ",  # Double spaces
            " ,": ",",
            " .": ".",
            " ?": "?",
            " !": "!",
        }
    
    def process_transcript_segments(
        self, 
        raw_segments: List[Dict[str, Any]],
        consultation_id: str
    ) -> List[TranscriptSegment]:
        """
        Process raw transcript segments into clean, structured format.
        
        Args:
            raw_segments: Raw transcript segments from ASR/diarization
            consultation_id: Associated consultation ID
            
        Returns:
            List of processed TranscriptSegment objects
        """
        processed_segments = []
        
        for i, segment in enumerate(raw_segments):
            try:
                # Clean the text
                cleaned_text = self._clean_text(segment.get("text", ""))
                
                if not cleaned_text.strip():
                    continue  # Skip empty segments
                
                # Create TranscriptSegment
                transcript_segment = TranscriptSegment(
                    consultation_id=consultation_id,
                    start_time=float(segment.get("start_time", 0)),
                    end_time=float(segment.get("end_time", 0)),
                    speaker=self._normalize_speaker(segment.get("speaker", SpeakerType.UNKNOWN)),
                    text=cleaned_text,
                    confidence=float(segment.get("confidence", 0.8)),
                    is_final=bool(segment.get("is_final", True))
                )
                
                processed_segments.append(transcript_segment)
                
            except Exception as e:
                logger.error(f"Error processing segment {i}: {e}")
                continue
        
        # Post-process segments
        processed_segments = self._merge_consecutive_segments(processed_segments)
        processed_segments = self._fix_timing_issues(processed_segments)
        
        return processed_segments
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize transcript text."""
        if not text:
            return ""
        
        # Convert to lowercase for processing
        cleaned = text.lower().strip()
        
        # Remove filler words and apply corrections
        for old, new in self.common_corrections.items():
            cleaned = cleaned.replace(old, new)
        
        # Apply medical term corrections
        for term, correction in self.medical_terms.items():
            cleaned = re.sub(r'\b' + re.escape(term) + r'\b', correction, cleaned)
        
        # Capitalize first letter of sentences
        sentences = re.split(r'[.!?]+', cleaned)
        cleaned_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                sentence = sentence[0].upper() + sentence[1:] if len(sentence) > 1 else sentence.upper()
                cleaned_sentences.append(sentence)
        
        # Join sentences back
        if cleaned_sentences:
            cleaned = '. '.join(cleaned_sentences)
            if not cleaned.endswith(('.', '!', '?')):
                cleaned += '.'
        
        # Final cleanup
        cleaned = re.sub(r'\s+', ' ', cleaned)  # Multiple spaces to single
        cleaned = cleaned.strip()
        
        return cleaned
    
    def _normalize_speaker(self, speaker: Any) -> SpeakerType:
        """Normalize speaker identification."""
        if isinstance(speaker, SpeakerType):
            return speaker
        
        if isinstance(speaker, str):
            speaker_lower = speaker.lower()
            if "doctor" in speaker_lower or "dr" in speaker_lower or "physician" in speaker_lower:
                return SpeakerType.DOCTOR
            elif "patient" in speaker_lower:
                return SpeakerType.PATIENT
            elif "other" in speaker_lower:
                return SpeakerType.OTHER
        
        return SpeakerType.UNKNOWN
    
    def _merge_consecutive_segments(self, segments: List[TranscriptSegment]) -> List[TranscriptSegment]:
        """Merge consecutive segments from the same speaker."""
        if not segments:
            return segments
        
        merged_segments = []
        current_segment = segments[0]
        
        for next_segment in segments[1:]:
            # Check if segments should be merged
            if (current_segment.speaker == next_segment.speaker and
                next_segment.start_time - current_segment.end_time < 2.0 and  # Less than 2 seconds gap
                len(current_segment.text) + len(next_segment.text) < 500):  # Not too long
                
                # Merge segments
                current_segment.text += " " + next_segment.text
                current_segment.end_time = next_segment.end_time
                current_segment.confidence = min(current_segment.confidence or 0, next_segment.confidence or 0)
            else:
                # Add current segment and start new one
                merged_segments.append(current_segment)
                current_segment = next_segment
        
        # Add the last segment
        merged_segments.append(current_segment)
        
        return merged_segments
    
    def _fix_timing_issues(self, segments: List[TranscriptSegment]) -> List[TranscriptSegment]:
        """Fix timing overlaps and gaps in segments."""
        if not segments:
            return segments
        
        # Sort by start time
        segments.sort(key=lambda x: x.start_time)
        
        fixed_segments = []
        
        for i, segment in enumerate(segments):
            if i == 0:
                fixed_segments.append(segment)
                continue
            
            prev_segment = fixed_segments[-1]
            
            # Fix overlaps
            if segment.start_time < prev_segment.end_time:
                # Adjust timing to prevent overlap
                gap = (prev_segment.end_time - segment.start_time) / 2
                prev_segment.end_time -= gap
                segment.start_time = prev_segment.end_time
            
            fixed_segments.append(segment)
        
        return fixed_segments
    
    def generate_summary_stats(self, segments: List[TranscriptSegment]) -> Dict[str, Any]:
        """Generate summary statistics for transcript."""
        if not segments:
            return {
                "total_duration": 0,
                "word_count": 0,
                "speaker_stats": {},
                "segment_count": 0
            }
        
        total_duration = max(seg.end_time for seg in segments) if segments else 0
        total_words = sum(len(seg.text.split()) for seg in segments)
        
        speaker_stats = {}
        for segment in segments:
            speaker = segment.speaker.value
            if speaker not in speaker_stats:
                speaker_stats[speaker] = {
                    "duration": 0,
                    "word_count": 0,
                    "segment_count": 0
                }
            
            speaker_stats[speaker]["duration"] += segment.end_time - segment.start_time
            speaker_stats[speaker]["word_count"] += len(segment.text.split())
            speaker_stats[speaker]["segment_count"] += 1
        
        return {
            "total_duration": total_duration,
            "word_count": total_words,
            "speaker_stats": speaker_stats,
            "segment_count": len(segments)
        }
    
    def filter_by_confidence(
        self, 
        segments: List[TranscriptSegment], 
        min_confidence: float = 0.5
    ) -> List[TranscriptSegment]:
        """Filter segments by confidence threshold."""
        return [seg for seg in segments if (seg.confidence or 0) >= min_confidence]
    
    def filter_by_speaker(
        self, 
        segments: List[TranscriptSegment], 
        speakers: List[SpeakerType]
    ) -> List[TranscriptSegment]:
        """Filter segments by speaker type."""
        return [seg for seg in segments if seg.speaker in speakers]


# Global transcript processor instance
transcript_processor = TranscriptProcessor()
