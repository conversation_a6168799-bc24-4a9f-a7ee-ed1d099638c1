import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;

import '../models/consultation.dart';
import '../models/patient_info.dart';
import '../models/transcript_segment.dart';
import '../utils/constants.dart';

/// Exception for API-related errors
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? details;

  const ApiException(this.message, {this.statusCode, this.details});

  @override
  String toString() {
    return 'ApiException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
  }
}

/// Service for handling API communication with the backend
class ApiService {
  static const String _baseUrl = AppConstants.apiBaseUrl;
  static const Duration _timeout = Duration(seconds: 30);

  final http.Client _client = http.Client();

  /// Start a new consultation
  Future<String> startConsultation({
    required PatientInfo patientInfo,
    String? doctorName,
    String language = AppConstants.defaultLanguage,
  }) async {
    try {
      final response = await _client
          .post(
            Uri.parse('$_baseUrl/consultations/start'),
            headers: _getHeaders(),
            body: jsonEncode({
              'patient_info': patientInfo.toJson(),
              'doctor_name': doctorName,
              'language': language,
            }),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['consultation_id'] as String;
      } else {
        throw ApiException(
          'Failed to start consultation',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Network error: ${e.toString()}');
    }
  }

  /// Send audio chunk for real-time transcription
  Future<AudioChunkResponse> sendAudioChunk({
    required String consultationId,
    required Uint8List audioData,
    required int chunkIndex,
    bool isFinal = false,
  }) async {
    try {
      final audioBase64 = base64Encode(audioData);

      final response = await _client
          .post(
            Uri.parse('$_baseUrl/audio/chunk'),
            headers: _getHeaders(consultationId: consultationId),
            body: jsonEncode({
              'audio_data': audioBase64,
              'chunk_index': chunkIndex,
              'is_final': isFinal,
            }),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return AudioChunkResponse.fromJson(data);
      } else {
        throw ApiException(
          'Failed to process audio chunk',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Network error: ${e.toString()}');
    }
  }

  /// Stop audio recording
  Future<void> stopRecording(String consultationId) async {
    try {
      final response = await _client
          .post(
            Uri.parse('$_baseUrl/audio/stop'),
            headers: _getHeaders(consultationId: consultationId),
          )
          .timeout(_timeout);

      if (response.statusCode != 200) {
        throw ApiException(
          'Failed to stop recording',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Network error: ${e.toString()}');
    }
  }

  /// Finish consultation and get processed transcript
  Future<FinishConsultationResponse> finishConsultation({
    required String consultationId,
    String? doctorNotes,
  }) async {
    try {
      final response = await _client
          .post(
            Uri.parse('$_baseUrl/consultations/finish'),
            headers: _getHeaders(consultationId: consultationId),
            body: jsonEncode({
              'doctor_notes': doctorNotes,
            }),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return FinishConsultationResponse.fromJson(data);
      } else {
        throw ApiException(
          'Failed to finish consultation',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Network error: ${e.toString()}');
    }
  }

  /// Generate PDF report
  Future<GeneratePdfResponse> generatePdf({
    required String consultationId,
    String? doctorNotes,
    bool includeCoverPage = true,
    bool includeTranscript = true,
    bool includeNotesPage = true,
  }) async {
    try {
      final response = await _client
          .post(
            Uri.parse('$_baseUrl/pdf/generate'),
            headers: _getHeaders(consultationId: consultationId),
            body: jsonEncode({
              'doctor_notes': doctorNotes,
              'include_cover_page': includeCoverPage,
              'include_transcript': includeTranscript,
              'include_notes_page': includeNotesPage,
            }),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return GeneratePdfResponse.fromJson(data);
      } else {
        throw ApiException(
          'Failed to generate PDF',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Network error: ${e.toString()}');
    }
  }

  /// Download PDF file
  Future<Uint8List> downloadPdf(String consultationId) async {
    try {
      final response = await _client
          .get(
            Uri.parse('$_baseUrl/pdf/download/$consultationId'),
            headers: _getHeaders(),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        return response.bodyBytes;
      } else {
        throw ApiException(
          'Failed to download PDF',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Network error: ${e.toString()}');
    }
  }

  /// Get consultation status
  Future<ConsultationStatusResponse> getConsultationStatus(String consultationId) async {
    try {
      final response = await _client
          .get(
            Uri.parse('$_baseUrl/consultations/$consultationId/status'),
            headers: _getHeaders(),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return ConsultationStatusResponse.fromJson(data);
      } else {
        throw ApiException(
          'Failed to get consultation status',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Network error: ${e.toString()}');
    }
  }

  /// List consultations
  Future<List<ConsultationStatusResponse>> listConsultations({
    int page = 1,
    int pageSize = 20,
    ConsultationStatus? status,
    String? doctorName,
    String? patientName,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'page_size': pageSize.toString(),
      };

      if (status != null) queryParams['status'] = status.value;
      if (doctorName != null) queryParams['doctor_name'] = doctorName;
      if (patientName != null) queryParams['patient_name'] = patientName;

      final uri = Uri.parse('$_baseUrl/consultations/').replace(
        queryParameters: queryParams,
      );

      final response = await _client
          .get(uri, headers: _getHeaders())
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data
            .map((item) => ConsultationStatusResponse.fromJson(item))
            .toList();
      } else {
        throw ApiException(
          'Failed to list consultations',
          statusCode: response.statusCode,
          details: response.body,
        );
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Network error: ${e.toString()}');
    }
  }

  /// Health check
  Future<bool> healthCheck() async {
    try {
      final response = await _client
          .get(
            Uri.parse('$_baseUrl/../health'),
            headers: _getHeaders(),
          )
          .timeout(_timeout);

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  /// Get common headers for API requests
  Map<String, String> _getHeaders({String? consultationId}) {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (consultationId != null) {
      headers['X-Consultation-ID'] = consultationId;
    }

    return headers;
  }

  /// Dispose resources
  void dispose() {
    _client.close();
  }
}

/// Response models for API calls
class AudioChunkResponse {
  final String consultationId;
  final int chunkIndex;
  final String? transcript;
  final bool isPartial;
  final double? confidence;
  final double? processingTimeMs;

  const AudioChunkResponse({
    required this.consultationId,
    required this.chunkIndex,
    this.transcript,
    this.isPartial = true,
    this.confidence,
    this.processingTimeMs,
  });

  factory AudioChunkResponse.fromJson(Map<String, dynamic> json) {
    return AudioChunkResponse(
      consultationId: json['consultation_id'] as String,
      chunkIndex: json['chunk_index'] as int,
      transcript: json['transcript'] as String?,
      isPartial: json['is_partial'] as bool? ?? true,
      confidence: json['confidence'] != null
          ? (json['confidence'] as num).toDouble()
          : null,
      processingTimeMs: json['processing_time_ms'] != null
          ? (json['processing_time_ms'] as num).toDouble()
          : null,
    );
  }
}

class FinishConsultationResponse {
  final String consultationId;
  final ConsultationStatus status;
  final List<TranscriptSegment> transcriptSegments;
  final TranscriptStats summaryStats;
  final double processingTimeMs;
  final String message;

  const FinishConsultationResponse({
    required this.consultationId,
    required this.status,
    required this.transcriptSegments,
    required this.summaryStats,
    required this.processingTimeMs,
    required this.message,
  });

  factory FinishConsultationResponse.fromJson(Map<String, dynamic> json) {
    final segmentsList = json['transcript_segments'] as List<dynamic>;
    final segments = segmentsList
        .map((segment) => TranscriptSegment.fromJson(segment as Map<String, dynamic>))
        .toList();

    return FinishConsultationResponse(
      consultationId: json['consultation_id'] as String,
      status: ConsultationStatus.fromString(json['status'] as String),
      transcriptSegments: segments,
      summaryStats: TranscriptStats.fromJson(json['summary_stats'] as Map<String, dynamic>),
      processingTimeMs: (json['processing_time_ms'] as num).toDouble(),
      message: json['message'] as String,
    );
  }
}

class GeneratePdfResponse {
  final String consultationId;
  final String? pdfUrl;
  final String pdfFilename;
  final int fileSizeBytes;
  final double generationTimeMs;
  final String message;

  const GeneratePdfResponse({
    required this.consultationId,
    this.pdfUrl,
    required this.pdfFilename,
    required this.fileSizeBytes,
    required this.generationTimeMs,
    required this.message,
  });

  factory GeneratePdfResponse.fromJson(Map<String, dynamic> json) {
    return GeneratePdfResponse(
      consultationId: json['consultation_id'] as String,
      pdfUrl: json['pdf_url'] as String?,
      pdfFilename: json['pdf_filename'] as String,
      fileSizeBytes: json['file_size_bytes'] as int,
      generationTimeMs: (json['generation_time_ms'] as num).toDouble(),
      message: json['message'] as String,
    );
  }
}

class ConsultationStatusResponse {
  final String consultationId;
  final ConsultationStatus status;
  final String patientName;
  final String? doctorName;
  final DateTime? startTime;
  final double? duration;
  final int transcriptCount;
  final bool hasPdf;

  const ConsultationStatusResponse({
    required this.consultationId,
    required this.status,
    required this.patientName,
    this.doctorName,
    this.startTime,
    this.duration,
    required this.transcriptCount,
    required this.hasPdf,
  });

  factory ConsultationStatusResponse.fromJson(Map<String, dynamic> json) {
    return ConsultationStatusResponse(
      consultationId: json['consultation_id'] as String,
      status: ConsultationStatus.fromString(json['status'] as String),
      patientName: json['patient_name'] as String,
      doctorName: json['doctor_name'] as String?,
      startTime: json['start_time'] != null
          ? DateTime.parse(json['start_time'] as String)
          : null,
      duration: json['duration'] != null
          ? (json['duration'] as num).toDouble()
          : null,
      transcriptCount: json['transcript_count'] as int,
      hasPdf: json['has_pdf'] as bool,
    );
  }
}
