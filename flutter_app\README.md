# DocTranscribe Flutter App

A cross-platform mobile application for medical consultation transcription and PDF generation.

## Features

- **Patient Information Management**: Capture and validate patient details
- **Real-time Audio Recording**: Record doctor-patient consultations
- **Live Transcription**: Real-time speech-to-text using NVIDIA Riva
- **Speaker Diarization**: Identify and separate doctor/patient voices
- **Transcript Review**: Edit and review transcribed conversations
- **PDF Generation**: Create professional consultation reports
- **Consultation History**: View and manage past consultations
- **Settings Management**: Configure app preferences and defaults

## Prerequisites

- Flutter SDK 3.10.0 or higher
- Dart SDK 3.0.0 or higher
- Android Studio / Xcode for mobile development
- A running DocTranscribe backend server

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd flutter_app
   ```

2. **Install dependencies**:
   ```bash
   flutter pub get
   ```

3. **Configure the backend URL**:
   Edit `lib/utils/constants.dart` and update the `apiBaseUrl`:
   ```dart
   static const String apiBaseUrl = 'http://your-backend-url:8000/api/v1';
   ```

4. **Run the app**:
   ```bash
   # For development
   flutter run

   # For specific platform
   flutter run -d android
   flutter run -d ios
   ```

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/                   # Data models
│   ├── consultation.dart
│   ├── patient_info.dart
│   └── transcript_segment.dart
├── providers/                # State management
│   ├── consultation_provider.dart
│   └── transcript_provider.dart
├── screens/                  # UI screens
│   ├── patient_info_screen.dart
│   ├── recording_screen.dart
│   ├── review_transcript_screen.dart
│   ├── history_screen.dart
│   └── settings_screen.dart
├── services/                 # Business logic
│   ├── api_service.dart
│   └── audio_service.dart
├── utils/                    # Utilities and constants
│   └── constants.dart
└── widgets/                  # Reusable UI components
```

## Configuration

### Backend Connection

Update the API base URL in `lib/utils/constants.dart`:

```dart
class AppConstants {
  static const String apiBaseUrl = 'http://localhost:8000/api/v1';
  // ... other constants
}
```

### Supported Languages

The app supports multiple languages for transcription:

- English (US) - `en-US`
- Spanish (Spain) - `es-ES`
- Hindi (India) - `hi-IN`
- French (France) - `fr-FR`
- German (Germany) - `de-DE`

Add more languages in `AppConstants.supportedLanguages`.

### Audio Configuration

Audio recording settings can be modified in `AppConstants`:

```dart
static const int audioSampleRate = 16000;
static const int audioChannels = 1;
static const int audioChunkDurationMs = 100;
```

## Permissions

The app requires the following permissions:

### Android (`android/app/src/main/AndroidManifest.xml`)

```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.INTERNET" />
```

### iOS (`ios/Runner/Info.plist`)

```xml
<key>NSMicrophoneUsageDescription</key>
<string>This app needs microphone access to record consultations</string>
<key>NSDocumentsDirectoryUsageDescription</key>
<string>This app needs storage access to save PDF reports</string>
```

## Usage

### Starting a Consultation

1. Open the app and fill in patient information
2. Enter doctor name and select transcription language
3. Tap "Start Consultation" to begin

### Recording

1. Tap the microphone button to start recording
2. Speak normally - the app will capture audio and show live transcription
3. Tap "Finish" when the consultation is complete

### Review and PDF Generation

1. Review the transcribed conversation
2. Edit any incorrect text if needed
3. Add doctor's notes
4. Generate and save PDF report

### Viewing History

1. Access consultation history from the main screen
2. View details of past consultations
3. Download previously generated PDFs

## Development

### State Management

The app uses Provider for state management:

- `ConsultationProvider`: Manages consultation lifecycle
- `TranscriptProvider`: Handles real-time transcription

### API Integration

All backend communication is handled through `ApiService`:

```dart
final apiService = ApiService();

// Start consultation
final consultationId = await apiService.startConsultation(
  patientInfo: patientInfo,
  doctorName: doctorName,
);

// Send audio chunk
await apiService.sendAudioChunk(
  consultationId: consultationId,
  audioData: audioData,
  chunkIndex: index,
);
```

### Audio Recording

Audio recording is managed by `AudioService`:

```dart
final audioService = AudioService();

// Start recording
await audioService.startRecording();

// Listen to audio stream
audioService.audioStream?.listen((audioChunk) {
  // Process audio chunk
});
```

## Building for Production

### Android

```bash
flutter build apk --release
# or
flutter build appbundle --release
```

### iOS

```bash
flutter build ios --release
```

## Testing

Run tests with:

```bash
flutter test
```

## Troubleshooting

### Common Issues

1. **Microphone Permission Denied**
   - Ensure microphone permissions are granted
   - Check device settings for app permissions

2. **Network Connection Error**
   - Verify backend server is running
   - Check API base URL configuration
   - Ensure device has internet connectivity

3. **Audio Recording Failed**
   - Check microphone hardware
   - Verify audio permissions
   - Try restarting the app

4. **PDF Generation Failed**
   - Check storage permissions
   - Ensure sufficient device storage
   - Verify backend PDF service is working

### Debug Mode

Enable debug logging by setting:

```dart
static const bool debug = true;
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
