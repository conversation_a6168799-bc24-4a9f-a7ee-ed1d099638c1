from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import FileResponse
import logging
import os
from datetime import datetime
from typing import Optional

from ..schemas import GeneratePDFRequest, GeneratePDFResponse
from ..dependencies import get_valid_consultation, update_consultation
from ...services.pdf_generator import pdf_generator
from ...models.consultation import Consultation
from ...models.transcript_segment import TranscriptSegment
from ...models.patient import PatientInfo
from ...core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/pdf", tags=["pdf"])


@router.post("/generate", response_model=GeneratePDFResponse)
async def generate_pdf(
    request: GeneratePDFRequest,
    consultation: dict = Depends(get_valid_consultation)
):
    """
    Generate a PDF report for a consultation.
    
    Creates a comprehensive PDF document including cover page, transcript,
    and doctor's notes.
    """
    try:
        consultation_id = consultation["id"]
        start_time = datetime.utcnow()
        
        logger.info(f"Generating PDF for consultation {consultation_id}")
        
        # Validate consultation has transcript data
        transcript_segments = consultation.get("transcript_segments", [])
        if not transcript_segments and request.include_transcript:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot generate PDF: No transcript data available"
            )
        
        # Convert consultation data to models
        patient_info_data = consultation.get("patient_info", {})
        patient_info = PatientInfo(**patient_info_data)
        
        consultation_model = Consultation(
            id=consultation_id,
            patient_info=patient_info,
            status=consultation.get("status", "unknown"),
            doctor_name=consultation.get("doctor_name"),
            start_time=consultation.get("start_time"),
            end_time=consultation.get("end_time"),
            duration=consultation.get("duration"),
            doctor_notes=request.doctor_notes or consultation.get("doctor_notes"),
            created_at=consultation.get("created_at", datetime.utcnow())
        )
        
        # Convert transcript segments to models
        transcript_models = []
        for segment_data in transcript_segments:
            try:
                segment = TranscriptSegment(**segment_data)
                transcript_models.append(segment)
            except Exception as e:
                logger.warning(f"Skipping invalid transcript segment: {e}")
                continue
        
        # Generate PDF
        pdf_path = await pdf_generator.generate_consultation_pdf(
            consultation=consultation_model,
            transcript_segments=transcript_models,
            doctor_notes=request.doctor_notes,
            output_dir=settings.pdf_output_dir
        )
        
        # Get file size
        file_size = os.path.getsize(pdf_path)
        
        # Update consultation with PDF path
        update_consultation(consultation_id, {
            "pdf_file_path": pdf_path,
            "doctor_notes": request.doctor_notes or consultation.get("doctor_notes")
        })
        
        processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        logger.info(f"Generated PDF for consultation {consultation_id} in {processing_time:.2f}ms")
        
        return GeneratePDFResponse(
            consultation_id=consultation_id,
            pdf_url=f"/pdf/download/{consultation_id}",
            pdf_filename=os.path.basename(pdf_path),
            file_size_bytes=file_size,
            generation_time_ms=processing_time,
            message="PDF generated successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating PDF: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate PDF: {str(e)}"
        )


@router.get("/download/{consultation_id}")
async def download_pdf(consultation_id: str):
    """
    Download the generated PDF for a consultation.
    """
    try:
        consultation = get_consultation(consultation_id)
        if not consultation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Consultation {consultation_id} not found"
            )
        
        pdf_path = consultation.get("pdf_file_path")
        if not pdf_path or not os.path.exists(pdf_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="PDF file not found. Please generate the PDF first."
            )
        
        # Get patient name for filename
        patient_info = consultation.get("patient_info", {})
        patient_name = patient_info.get("name", "Unknown").replace(" ", "_")
        
        filename = f"consultation_{patient_name}_{consultation_id[:8]}.pdf"
        
        return FileResponse(
            path=pdf_path,
            filename=filename,
            media_type="application/pdf"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading PDF: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download PDF: {str(e)}"
        )


@router.get("/preview/{consultation_id}")
async def preview_pdf_info(consultation_id: str):
    """
    Get PDF information without downloading the file.
    """
    try:
        consultation = get_consultation(consultation_id)
        if not consultation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Consultation {consultation_id} not found"
            )
        
        pdf_path = consultation.get("pdf_file_path")
        has_pdf = pdf_path and os.path.exists(pdf_path)
        
        file_size = 0
        created_at = None
        
        if has_pdf:
            file_size = os.path.getsize(pdf_path)
            created_at = datetime.fromtimestamp(os.path.getctime(pdf_path))
        
        patient_info = consultation.get("patient_info", {})
        transcript_segments = consultation.get("transcript_segments", [])
        
        return {
            "consultation_id": consultation_id,
            "has_pdf": has_pdf,
            "pdf_filename": os.path.basename(pdf_path) if pdf_path else None,
            "file_size_bytes": file_size,
            "created_at": created_at,
            "patient_name": patient_info.get("name"),
            "doctor_name": consultation.get("doctor_name"),
            "consultation_date": consultation.get("start_time"),
            "transcript_segments_count": len(transcript_segments),
            "has_doctor_notes": bool(consultation.get("doctor_notes"))
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting PDF preview: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get PDF preview: {str(e)}"
        )


@router.delete("/{consultation_id}")
async def delete_pdf(consultation_id: str):
    """
    Delete the PDF file for a consultation.
    """
    try:
        consultation = get_consultation(consultation_id)
        if not consultation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Consultation {consultation_id} not found"
            )
        
        pdf_path = consultation.get("pdf_file_path")
        if not pdf_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No PDF file associated with this consultation"
            )
        
        # Delete the file if it exists
        if os.path.exists(pdf_path):
            os.remove(pdf_path)
            logger.info(f"Deleted PDF file: {pdf_path}")
        
        # Update consultation to remove PDF reference
        update_consultation(consultation_id, {"pdf_file_path": None})
        
        return {
            "message": "PDF deleted successfully",
            "consultation_id": consultation_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting PDF: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete PDF: {str(e)}"
        )


# Import get_consultation function
from ..dependencies import get_consultation
