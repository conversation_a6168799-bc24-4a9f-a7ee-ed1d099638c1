from typing import Op<PERSON>, Dict, Any
from fastapi import HTT<PERSON><PERSON><PERSON><PERSON>, Head<PERSON>, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging

from ..core.security import verify_token
from ..core.utils import generate_consultation_id

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer(auto_error=False)

# In-memory storage for consultations (in production, use a proper database)
consultations_store: Dict[str, Dict[str, Any]] = {}
audio_buffers: Dict[str, list] = {}


async def get_consultation_id(
    x_consultation_id: Optional[str] = Header(None, alias="X-Consultation-ID")
) -> str:
    """
    Extract consultation ID from header.
    
    Args:
        x_consultation_id: Consultation ID from header
        
    Returns:
        Consultation ID
        
    Raises:
        HTTPException: If consultation ID is missing or invalid
    """
    if not x_consultation_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="X-Consultation-ID header is required"
        )
    
    return x_consultation_id


async def get_valid_consultation(
    consultation_id: str = Depends(get_consultation_id)
) -> Dict[str, Any]:
    """
    Get and validate consultation from store.
    
    Args:
        consultation_id: Consultation ID
        
    Returns:
        Consultation data
        
    Raises:
        HTTPException: If consultation not found
    """
    consultation = consultations_store.get(consultation_id)
    if not consultation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Consultation {consultation_id} not found"
        )
    
    return consultation


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[Dict[str, Any]]:
    """
    Get current user from JWT token (optional for this demo).
    
    Args:
        credentials: HTTP authorization credentials
        
    Returns:
        User data if authenticated, None otherwise
    """
    if not credentials:
        return None
    
    try:
        payload = verify_token(credentials.credentials)
        return payload
    except Exception as e:
        logger.warning(f"Invalid token: {e}")
        return None


def create_consultation(patient_info: Dict[str, Any], doctor_name: Optional[str] = None) -> str:
    """
    Create a new consultation in the store.
    
    Args:
        patient_info: Patient information
        doctor_name: Doctor's name
        
    Returns:
        Consultation ID
    """
    consultation_id = generate_consultation_id()
    
    consultation_data = {
        "id": consultation_id,
        "patient_info": patient_info,
        "doctor_name": doctor_name,
        "status": "created",
        "start_time": None,
        "end_time": None,
        "duration": None,
        "transcript_segments": [],
        "audio_chunks": [],
        "doctor_notes": None,
        "pdf_file_path": None,
        "created_at": None,
        "updated_at": None
    }
    
    consultations_store[consultation_id] = consultation_data
    audio_buffers[consultation_id] = []
    
    logger.info(f"Created consultation {consultation_id} for patient {patient_info.get('name')}")
    
    return consultation_id


def update_consultation(consultation_id: str, updates: Dict[str, Any]) -> None:
    """
    Update consultation data in the store.
    
    Args:
        consultation_id: Consultation ID
        updates: Dictionary of updates to apply
    """
    if consultation_id in consultations_store:
        consultations_store[consultation_id].update(updates)
        consultations_store[consultation_id]["updated_at"] = None  # Would set current time
        logger.info(f"Updated consultation {consultation_id}")
    else:
        logger.error(f"Attempted to update non-existent consultation {consultation_id}")


def get_consultation(consultation_id: str) -> Optional[Dict[str, Any]]:
    """
    Get consultation data from store.
    
    Args:
        consultation_id: Consultation ID
        
    Returns:
        Consultation data or None if not found
    """
    return consultations_store.get(consultation_id)


def add_audio_chunk(consultation_id: str, audio_data: bytes, chunk_index: int) -> None:
    """
    Add audio chunk to consultation buffer.
    
    Args:
        consultation_id: Consultation ID
        audio_data: Audio chunk data
        chunk_index: Chunk index
    """
    if consultation_id not in audio_buffers:
        audio_buffers[consultation_id] = []
    
    audio_buffers[consultation_id].append({
        "index": chunk_index,
        "data": audio_data,
        "timestamp": None  # Would set current time
    })
    
    logger.debug(f"Added audio chunk {chunk_index} to consultation {consultation_id}")


def get_audio_buffer(consultation_id: str) -> list:
    """
    Get audio buffer for consultation.
    
    Args:
        consultation_id: Consultation ID
        
    Returns:
        List of audio chunks
    """
    return audio_buffers.get(consultation_id, [])


def clear_audio_buffer(consultation_id: str) -> None:
    """
    Clear audio buffer for consultation.
    
    Args:
        consultation_id: Consultation ID
    """
    if consultation_id in audio_buffers:
        audio_buffers[consultation_id].clear()
        logger.info(f"Cleared audio buffer for consultation {consultation_id}")


def cleanup_consultation(consultation_id: str) -> None:
    """
    Clean up consultation data and buffers.
    
    Args:
        consultation_id: Consultation ID
    """
    if consultation_id in consultations_store:
        del consultations_store[consultation_id]
    
    if consultation_id in audio_buffers:
        del audio_buffers[consultation_id]
    
    logger.info(f"Cleaned up consultation {consultation_id}")


def get_all_consultations() -> Dict[str, Dict[str, Any]]:
    """
    Get all consultations (for admin/debugging purposes).
    
    Returns:
        Dictionary of all consultations
    """
    return consultations_store.copy()


# Validation helpers
def validate_audio_chunk_size(audio_data: bytes, max_size: int = 1048576) -> bool:
    """
    Validate audio chunk size.
    
    Args:
        audio_data: Audio chunk data
        max_size: Maximum allowed size in bytes
        
    Returns:
        True if valid, False otherwise
    """
    return len(audio_data) <= max_size


def validate_consultation_status(consultation: Dict[str, Any], required_status: str) -> bool:
    """
    Validate consultation status.
    
    Args:
        consultation: Consultation data
        required_status: Required status
        
    Returns:
        True if status matches, False otherwise
    """
    return consultation.get("status") == required_status
