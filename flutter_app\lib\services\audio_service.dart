import 'dart:async';
import 'dart:typed_data';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';

import '../utils/constants.dart';

/// Exception for audio-related errors
class AudioException implements Exception {
  final String message;
  final String? details;

  const AudioException(this.message, {this.details});

  @override
  String toString() {
    return 'AudioException: $message${details != null ? ' ($details)' : ''}';
  }
}

/// Audio recording state
enum AudioRecordingState {
  idle,
  recording,
  paused,
  stopped,
  error,
}

/// Audio service for handling recording and streaming
class AudioService {
  final AudioRecorder _recorder = AudioRecorder();
  
  AudioRecordingState _state = AudioRecordingState.idle;
  StreamController<Uint8List>? _audioStreamController;
  StreamController<double>? _amplitudeController;
  Timer? _chunkTimer;
  DateTime? _recordingStartTime;
  Duration _recordingDuration = Duration.zero;
  
  // Audio configuration
  static const RecordConfig _recordConfig = RecordConfig(
    encoder: AudioEncoder.pcm16bits,
    sampleRate: AppConstants.audioSampleRate,
    numChannels: AppConstants.audioChannels,
    bitRate: 256000,
  );

  /// Current recording state
  AudioRecordingState get state => _state;

  /// Stream of audio chunks
  Stream<Uint8List>? get audioStream => _audioStreamController?.stream;

  /// Stream of audio amplitude levels (0.0 to 1.0)
  Stream<double>? get amplitudeStream => _amplitudeController?.stream;

  /// Current recording duration
  Duration get recordingDuration => _recordingDuration;

  /// Check if currently recording
  bool get isRecording => _state == AudioRecordingState.recording;

  /// Check if recording is paused
  bool get isPaused => _state == AudioRecordingState.paused;

  /// Check if recording is stopped
  bool get isStopped => _state == AudioRecordingState.stopped;

  /// Request microphone permission
  Future<bool> requestPermission() async {
    try {
      final status = await Permission.microphone.request();
      return status == PermissionStatus.granted;
    } catch (e) {
      throw AudioException('Failed to request microphone permission', details: e.toString());
    }
  }

  /// Check if microphone permission is granted
  Future<bool> hasPermission() async {
    try {
      final status = await Permission.microphone.status;
      return status == PermissionStatus.granted;
    } catch (e) {
      return false;
    }
  }

  /// Start audio recording
  Future<void> startRecording() async {
    try {
      // Check permission
      if (!await hasPermission()) {
        if (!await requestPermission()) {
          throw AudioException(ErrorMessages.audioPermissionDenied);
        }
      }

      // Check if already recording
      if (_state == AudioRecordingState.recording) {
        throw AudioException('Recording is already in progress');
      }

      // Initialize stream controllers
      _audioStreamController = StreamController<Uint8List>.broadcast();
      _amplitudeController = StreamController<double>.broadcast();

      // Start recording
      await _recorder.start(_recordConfig);
      
      _state = AudioRecordingState.recording;
      _recordingStartTime = DateTime.now();
      _recordingDuration = Duration.zero;

      // Start chunk timer for streaming
      _startChunkTimer();

      // Start amplitude monitoring
      _startAmplitudeMonitoring();

    } catch (e) {
      _state = AudioRecordingState.error;
      await _cleanup();
      
      if (e is AudioException) {
        rethrow;
      } else {
        throw AudioException(ErrorMessages.audioRecordingFailed, details: e.toString());
      }
    }
  }

  /// Stop audio recording
  Future<void> stopRecording() async {
    try {
      if (_state != AudioRecordingState.recording && _state != AudioRecordingState.paused) {
        return;
      }

      // Stop recording
      await _recorder.stop();
      
      _state = AudioRecordingState.stopped;
      
      // Stop timers
      _chunkTimer?.cancel();
      
      // Close stream controllers
      await _audioStreamController?.close();
      await _amplitudeController?.close();
      
      await _cleanup();

    } catch (e) {
      _state = AudioRecordingState.error;
      await _cleanup();
      throw AudioException('Failed to stop recording', details: e.toString());
    }
  }

  /// Pause audio recording
  Future<void> pauseRecording() async {
    try {
      if (_state != AudioRecordingState.recording) {
        return;
      }

      await _recorder.pause();
      _state = AudioRecordingState.paused;
      
      // Pause timers
      _chunkTimer?.cancel();

    } catch (e) {
      _state = AudioRecordingState.error;
      throw AudioException('Failed to pause recording', details: e.toString());
    }
  }

  /// Resume audio recording
  Future<void> resumeRecording() async {
    try {
      if (_state != AudioRecordingState.paused) {
        return;
      }

      await _recorder.resume();
      _state = AudioRecordingState.recording;
      
      // Resume chunk timer
      _startChunkTimer();

    } catch (e) {
      _state = AudioRecordingState.error;
      throw AudioException('Failed to resume recording', details: e.toString());
    }
  }

  /// Get current amplitude level
  Future<double> getCurrentAmplitude() async {
    try {
      if (_state != AudioRecordingState.recording) {
        return 0.0;
      }

      final amplitude = await _recorder.getAmplitude();
      return amplitude.current.clamp(0.0, 1.0);
    } catch (e) {
      return 0.0;
    }
  }

  /// Start chunk timer for streaming audio data
  void _startChunkTimer() {
    _chunkTimer?.cancel();
    
    _chunkTimer = Timer.periodic(
      Duration(milliseconds: AppConstants.audioChunkDurationMs),
      (timer) async {
        if (_state == AudioRecordingState.recording) {
          await _processAudioChunk();
          _updateRecordingDuration();
        }
      },
    );
  }

  /// Process audio chunk (placeholder - actual implementation would capture audio data)
  Future<void> _processAudioChunk() async {
    try {
      // In a real implementation, you would capture the actual audio data here
      // For now, we'll create a dummy chunk to demonstrate the flow
      
      final chunkSize = (AppConstants.audioSampleRate * 
                        AppConstants.audioChannels * 
                        AppConstants.audioChunkDurationMs / 1000 * 2).round();
      
      final dummyChunk = Uint8List(chunkSize);
      // Fill with silence (zeros) - in real implementation, this would be actual audio data
      
      // Add to stream if controller is available
      if (_audioStreamController != null && !_audioStreamController!.isClosed) {
        _audioStreamController!.add(dummyChunk);
      }
      
    } catch (e) {
      // Log error but don't stop recording
      print('Error processing audio chunk: $e');
    }
  }

  /// Start amplitude monitoring
  void _startAmplitudeMonitoring() {
    Timer.periodic(Duration(milliseconds: 50), (timer) async {
      if (_state != AudioRecordingState.recording) {
        timer.cancel();
        return;
      }

      try {
        final amplitude = await getCurrentAmplitude();
        if (_amplitudeController != null && !_amplitudeController!.isClosed) {
          _amplitudeController!.add(amplitude);
        }
      } catch (e) {
        // Ignore amplitude errors
      }
    });
  }

  /// Update recording duration
  void _updateRecordingDuration() {
    if (_recordingStartTime != null) {
      _recordingDuration = DateTime.now().difference(_recordingStartTime!);
    }
  }

  /// Cleanup resources
  Future<void> _cleanup() async {
    _chunkTimer?.cancel();
    _chunkTimer = null;
    
    _audioStreamController = null;
    _amplitudeController = null;
    
    _recordingStartTime = null;
    _recordingDuration = Duration.zero;
  }

  /// Dispose the audio service
  Future<void> dispose() async {
    await stopRecording();
    await _recorder.dispose();
  }

  /// Check if device supports recording
  Future<bool> isRecordingSupported() async {
    try {
      return await _recorder.hasPermission();
    } catch (e) {
      return false;
    }
  }

  /// Get available audio input devices (if supported)
  Future<List<InputDevice>> getInputDevices() async {
    try {
      return await _recorder.listInputDevices();
    } catch (e) {
      return [];
    }
  }

  /// Validate recording duration
  bool isValidRecordingDuration() {
    return _recordingDuration >= AppConstants.minRecordingDuration &&
           _recordingDuration <= AppConstants.maxRecordingDuration;
  }

  /// Get formatted recording duration
  String getFormattedDuration() {
    final duration = _recordingDuration;
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
             '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    }
  }
}
