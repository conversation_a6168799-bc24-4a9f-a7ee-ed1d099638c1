import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:typed_data';

import '../models/transcript_segment.dart';
import '../services/api_service.dart';
import '../services/audio_service.dart';

/// Provider for managing transcript state and real-time transcription
class TranscriptProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();
  final AudioService _audioService = AudioService();

  List<TranscriptSegment> _transcriptSegments = [];
  List<TranscriptSegment> _partialSegments = [];
  String _liveTranscript = '';
  bool _isTranscribing = false;
  String? _error;
  int _chunkIndex = 0;
  StreamSubscription<Uint8List>? _audioStreamSubscription;
  String? _currentConsultationId;

  /// All transcript segments (final)
  List<TranscriptSegment> get transcriptSegments => List.unmodifiable(_transcriptSegments);

  /// Partial transcript segments (real-time)
  List<TranscriptSegment> get partialSegments => List.unmodifiable(_partialSegments);

  /// Live transcript text
  String get liveTranscript => _liveTranscript;

  /// Transcription state
  bool get isTranscribing => _isTranscribing;

  /// Error message
  String? get error => _error;

  /// Total word count
  int get totalWordCount {
    return _transcriptSegments.fold(0, (sum, segment) => sum + segment.wordCount);
  }

  /// Total duration
  double get totalDuration {
    if (_transcriptSegments.isEmpty) return 0.0;
    return _transcriptSegments.last.endTime;
  }

  /// Speaker statistics
  Map<SpeakerType, SpeakerStats> get speakerStats {
    final stats = <SpeakerType, SpeakerStats>{};
    
    for (final segment in _transcriptSegments) {
      if (!stats.containsKey(segment.speaker)) {
        stats[segment.speaker] = SpeakerStats(
          duration: 0,
          wordCount: 0,
          segmentCount: 0,
        );
      }
      
      final currentStats = stats[segment.speaker]!;
      stats[segment.speaker] = SpeakerStats(
        duration: currentStats.duration + segment.duration,
        wordCount: currentStats.wordCount + segment.wordCount,
        segmentCount: currentStats.segmentCount + 1,
      );
    }
    
    return stats;
  }

  /// Start real-time transcription
  Future<void> startTranscription(String consultationId) async {
    try {
      _currentConsultationId = consultationId;
      _isTranscribing = true;
      _chunkIndex = 0;
      _clearError();
      
      // Clear previous data
      _transcriptSegments.clear();
      _partialSegments.clear();
      _liveTranscript = '';
      
      // Start audio recording
      await _audioService.startRecording();
      
      // Subscribe to audio stream
      _audioStreamSubscription = _audioService.audioStream?.listen(
        _processAudioChunk,
        onError: (error) {
          _setError('Audio stream error: $error');
        },
      );
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to start transcription: ${e.toString()}');
      _isTranscribing = false;
      notifyListeners();
    }
  }

  /// Stop real-time transcription
  Future<void> stopTranscription() async {
    try {
      _isTranscribing = false;
      
      // Stop audio recording
      await _audioService.stopRecording();
      
      // Cancel audio stream subscription
      await _audioStreamSubscription?.cancel();
      _audioStreamSubscription = null;
      
      // Send final chunk if needed
      if (_currentConsultationId != null) {
        await _apiService.stopRecording(_currentConsultationId!);
      }
      
      // Convert partial segments to final
      _finalizePartialSegments();
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to stop transcription: ${e.toString()}');
    }
  }

  /// Process audio chunk for transcription
  Future<void> _processAudioChunk(Uint8List audioData) async {
    if (!_isTranscribing || _currentConsultationId == null) return;

    try {
      final response = await _apiService.sendAudioChunk(
        consultationId: _currentConsultationId!,
        audioData: audioData,
        chunkIndex: _chunkIndex,
        isFinal: false,
      );

      // Update live transcript
      if (response.transcript != null && response.transcript!.isNotEmpty) {
        _updateLiveTranscript(response.transcript!, response.isPartial);
      }

      _chunkIndex++;
    } catch (e) {
      // Log error but don't stop transcription
      print('Error processing audio chunk: $e');
    }
  }

  /// Update live transcript with new text
  void _updateLiveTranscript(String text, bool isPartial) {
    if (isPartial) {
      _liveTranscript = text;
    } else {
      // Add to final segments
      final segment = TranscriptSegment(
        consultationId: _currentConsultationId!,
        startTime: _chunkIndex * 0.1, // Approximate timing
        endTime: (_chunkIndex + 1) * 0.1,
        speaker: SpeakerType.unknown, // Will be determined by diarization
        text: text,
        isFinal: true,
        timestamp: DateTime.now(),
      );
      
      _transcriptSegments.add(segment);
      _liveTranscript = '';
    }
    
    notifyListeners();
  }

  /// Finalize partial segments
  void _finalizePartialSegments() {
    for (final partial in _partialSegments) {
      if (!_transcriptSegments.any((segment) => segment.id == partial.id)) {
        _transcriptSegments.add(partial.copyWith(isFinal: true));
      }
    }
    _partialSegments.clear();
  }

  /// Update transcript segments with processed data
  void updateTranscriptSegments(List<TranscriptSegment> segments) {
    _transcriptSegments = List.from(segments);
    _partialSegments.clear();
    _liveTranscript = '';
    notifyListeners();
  }

  /// Edit transcript segment
  void editTranscriptSegment(int index, String newText) {
    if (index >= 0 && index < _transcriptSegments.length) {
      _transcriptSegments[index] = _transcriptSegments[index].copyWith(
        text: newText,
      );
      notifyListeners();
    }
  }

  /// Update speaker for segment
  void updateSegmentSpeaker(int index, SpeakerType speaker) {
    if (index >= 0 && index < _transcriptSegments.length) {
      _transcriptSegments[index] = _transcriptSegments[index].copyWith(
        speaker: speaker,
      );
      notifyListeners();
    }
  }

  /// Remove transcript segment
  void removeTranscriptSegment(int index) {
    if (index >= 0 && index < _transcriptSegments.length) {
      _transcriptSegments.removeAt(index);
      notifyListeners();
    }
  }

  /// Add new transcript segment
  void addTranscriptSegment(TranscriptSegment segment) {
    _transcriptSegments.add(segment);
    _sortSegmentsByTime();
    notifyListeners();
  }

  /// Sort segments by start time
  void _sortSegmentsByTime() {
    _transcriptSegments.sort((a, b) => a.startTime.compareTo(b.startTime));
  }

  /// Filter segments by speaker
  List<TranscriptSegment> getSegmentsBySpeaker(SpeakerType speaker) {
    return _transcriptSegments.where((segment) => segment.speaker == speaker).toList();
  }

  /// Filter segments by confidence threshold
  List<TranscriptSegment> getSegmentsByConfidence(double minConfidence) {
    return _transcriptSegments
        .where((segment) => (segment.confidence ?? 0.0) >= minConfidence)
        .toList();
  }

  /// Get segments in time range
  List<TranscriptSegment> getSegmentsInTimeRange(double startTime, double endTime) {
    return _transcriptSegments
        .where((segment) => 
            segment.startTime >= startTime && segment.endTime <= endTime)
        .toList();
  }

  /// Search segments by text
  List<TranscriptSegment> searchSegments(String query) {
    final lowerQuery = query.toLowerCase();
    return _transcriptSegments
        .where((segment) => segment.text.toLowerCase().contains(lowerQuery))
        .toList();
  }

  /// Export transcript as text
  String exportAsText({bool includeTimestamps = true, bool includeSpeakers = true}) {
    final buffer = StringBuffer();
    
    for (final segment in _transcriptSegments) {
      if (includeTimestamps) {
        buffer.write('[${segment.formattedStartTime}] ');
      }
      
      if (includeSpeakers) {
        buffer.write('${segment.speaker.displayName}: ');
      }
      
      buffer.writeln(segment.text);
    }
    
    return buffer.toString();
  }

  /// Get transcript summary
  TranscriptStats getTranscriptSummary() {
    return TranscriptStats(
      totalDuration: totalDuration,
      wordCount: totalWordCount,
      speakerStats: speakerStats,
      segmentCount: _transcriptSegments.length,
      createdAt: DateTime.now(),
    );
  }

  /// Clear all transcript data
  void clearTranscript() {
    _transcriptSegments.clear();
    _partialSegments.clear();
    _liveTranscript = '';
    _chunkIndex = 0;
    _clearError();
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
  }

  /// Dispose resources
  @override
  void dispose() {
    _audioStreamSubscription?.cancel();
    _audioService.dispose();
    _apiService.dispose();
    super.dispose();
  }
}
