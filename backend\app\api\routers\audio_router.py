from fastapi import APIRouter, HTTPException, Depends, status, UploadFile, File
from typing import Optional
import logging
import base64
import asyncio
from datetime import datetime

from app.api.schemas import AudioChunkRequest, AudioChunkResponse, ErrorResponse
from app.api.dependencies import (
    get_valid_consultation, update_consultation, add_audio_chunk,
    validate_audio_chunk_size, validate_consultation_status
)
from app.services.riva_client import riva_client
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/audio", tags=["audio"])


@router.post("/chunk", response_model=AudioChunkResponse)
async def process_audio_chunk(
    request: AudioChunkRequest,
    consultation: dict = Depends(get_valid_consultation)
):
    """
    Process an audio chunk for real-time transcription.
    
    Accepts base64-encoded audio data, sends it to NVIDIA Riva for transcription,
    and returns partial or final transcript results.
    """
    try:
        consultation_id = consultation["id"]
        start_time = datetime.utcnow()
        
        logger.debug(f"Processing audio chunk {request.chunk_index} for consultation {consultation_id}")
        
        # Validate consultation status
        if not validate_consultation_status(consultation, "created") and \
           not validate_consultation_status(consultation, "recording"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot process audio for consultation in status: {consultation.get('status')}"
            )
        
        # Decode audio data
        try:
            audio_data = base64.b64decode(request.audio_data)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid base64 audio data: {str(e)}"
            )
        
        # Validate audio chunk size
        if not validate_audio_chunk_size(audio_data, settings.max_audio_chunk_size):
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"Audio chunk too large. Maximum size: {settings.max_audio_chunk_size} bytes"
            )
        
        # Update consultation status to recording if this is the first chunk
        if consultation.get("status") == "created":
            update_consultation(consultation_id, {
                "status": "recording",
                "start_time": datetime.utcnow()
            })
        
        # Store audio chunk
        add_audio_chunk(consultation_id, audio_data, request.chunk_index)
        
        # Process with NVIDIA Riva (simplified for demo)
        transcript_text = None
        confidence = None
        
        try:
            # For demo purposes, we'll simulate transcription
            # In production, you'd stream this to NVIDIA Riva
            if len(audio_data) > 1000:  # Only process if chunk has sufficient data
                # Simulate API call delay
                await asyncio.sleep(0.1)
                
                # Mock transcription result
                transcript_text = f"Transcribed text for chunk {request.chunk_index}"
                confidence = 0.85
                
                # Store partial transcript
                current_segments = consultation.get("transcript_segments", [])
                
                # Create or update segment for this chunk
                segment = {
                    "start_time": request.chunk_index * 0.1,  # Approximate timing
                    "end_time": (request.chunk_index + 1) * 0.1,
                    "text": transcript_text,
                    "confidence": confidence,
                    "is_final": request.is_final,
                    "speaker": "unknown",  # Will be determined by diarization
                    "chunk_index": request.chunk_index
                }
                
                current_segments.append(segment)
                update_consultation(consultation_id, {
                    "transcript_segments": current_segments
                })
        
        except Exception as e:
            logger.error(f"Error processing audio with Riva: {e}")
            # Continue without transcription for now
        
        processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        logger.debug(f"Processed chunk {request.chunk_index} in {processing_time:.2f}ms")
        
        return AudioChunkResponse(
            consultation_id=consultation_id,
            chunk_index=request.chunk_index,
            transcript=transcript_text,
            is_partial=not request.is_final,
            confidence=confidence,
            processing_time_ms=processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing audio chunk: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process audio chunk: {str(e)}"
        )


@router.post("/upload", response_model=dict)
async def upload_audio_file(
    file: UploadFile = File(...),
    consultation: dict = Depends(get_valid_consultation)
):
    """
    Upload a complete audio file for batch transcription.
    
    Alternative to streaming chunks - upload entire audio file at once.
    """
    try:
        consultation_id = consultation["id"]
        
        logger.info(f"Uploading audio file for consultation {consultation_id}")
        
        # Validate file type
        if not file.content_type or not file.content_type.startswith("audio/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be an audio file"
            )
        
        # Read file data
        audio_data = await file.read()
        
        # Validate file size
        if len(audio_data) > settings.max_audio_chunk_size * 100:  # Allow larger files for upload
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Audio file too large"
            )
        
        # Save file temporarily (in production, use proper file storage)
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
            temp_file.write(audio_data)
            temp_file_path = temp_file.name
        
        try:
            # Process with NVIDIA Riva
            transcription_result = await riva_client.transcribe_file(
                temp_file_path,
                language=consultation.get("language", "en-US")
            )
            
            # Update consultation with results
            update_consultation(consultation_id, {
                "status": "processing",
                "audio_file_path": temp_file_path,
                "transcript_text": transcription_result.get("text", ""),
                "end_time": datetime.utcnow()
            })
            
            return {
                "message": "Audio file uploaded and processed successfully",
                "consultation_id": consultation_id,
                "transcript_preview": transcription_result.get("text", "")[:200] + "..."
            }
            
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading audio file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload audio file: {str(e)}"
        )


@router.post("/stop")
async def stop_recording(consultation: dict = Depends(get_valid_consultation)):
    """
    Stop audio recording for a consultation.
    
    Signals the end of audio input and prepares for final processing.
    """
    try:
        consultation_id = consultation["id"]
        
        logger.info(f"Stopping recording for consultation {consultation_id}")
        
        # Validate consultation status
        if not validate_consultation_status(consultation, "recording"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot stop recording for consultation in status: {consultation.get('status')}"
            )
        
        # Update consultation status
        update_consultation(consultation_id, {
            "status": "recorded",
            "end_time": datetime.utcnow()
        })
        
        # Calculate duration
        start_time = consultation.get("start_time")
        end_time = datetime.utcnow()
        duration = None
        
        if start_time:
            duration = (end_time - start_time).total_seconds()
            update_consultation(consultation_id, {"duration": duration})
        
        return {
            "message": "Recording stopped successfully",
            "consultation_id": consultation_id,
            "duration": duration,
            "status": "recorded"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error stopping recording: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop recording: {str(e)}"
        )


@router.get("/status")
async def get_audio_status(consultation: dict = Depends(get_valid_consultation)):
    """
    Get audio processing status for a consultation.
    """
    try:
        consultation_id = consultation["id"]
        
        transcript_segments = consultation.get("transcript_segments", [])
        audio_chunks = consultation.get("audio_chunks", [])
        
        return {
            "consultation_id": consultation_id,
            "status": consultation.get("status"),
            "chunks_received": len(audio_chunks),
            "transcript_segments": len(transcript_segments),
            "duration": consultation.get("duration"),
            "has_audio_file": bool(consultation.get("audio_file_path"))
        }
        
    except Exception as e:
        logger.error(f"Error getting audio status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get audio status: {str(e)}"
        )
