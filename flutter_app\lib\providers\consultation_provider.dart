import 'package:flutter/foundation.dart';

import '../models/consultation.dart';
import '../models/patient_info.dart';
import '../services/api_service.dart';
import '../utils/constants.dart';

/// Provider for managing consultation state
class ConsultationProvider extends ChangeNotifier {
  final ApiService _apiService = ApiService();

  Consultation? _currentConsultation;
  List<Consultation> _consultationHistory = [];
  bool _isLoading = false;
  String? _error;
  String? _currentConsultationId;

  /// Current consultation
  Consultation? get currentConsultation => _currentConsultation;

  /// Consultation history
  List<Consultation> get consultationHistory => List.unmodifiable(_consultationHistory);

  /// Loading state
  bool get isLoading => _isLoading;

  /// Error message
  String? get error => _error;

  /// Current consultation ID
  String? get currentConsultationId => _currentConsultationId;

  /// Check if there's an active consultation
  bool get hasActiveConsultation => _currentConsultation != null;

  /// Check if current consultation is recording
  bool get isRecording => _currentConsultation?.status == ConsultationStatus.recording;

  /// Check if current consultation is processing
  bool get isProcessing => _currentConsultation?.status == ConsultationStatus.processing;

  /// Check if current consultation is completed
  bool get isCompleted => _currentConsultation?.status == ConsultationStatus.completed;

  /// Start a new consultation
  Future<void> startConsultation({
    required PatientInfo patientInfo,
    String? doctorName,
    String language = AppConstants.defaultLanguage,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      // Validate patient info
      final validationErrors = patientInfo.validate();
      if (validationErrors.isNotEmpty) {
        throw Exception('Invalid patient information: ${validationErrors.join(', ')}');
      }

      // Call API to start consultation
      final consultationId = await _apiService.startConsultation(
        patientInfo: patientInfo,
        doctorName: doctorName,
        language: language,
      );

      // Create consultation object
      _currentConsultation = Consultation(
        id: consultationId,
        patientInfo: patientInfo,
        status: ConsultationStatus.created,
        doctorName: doctorName,
        createdAt: DateTime.now(),
        language: language,
      );

      _currentConsultationId = consultationId;

      notifyListeners();
    } catch (e) {
      _setError('Failed to start consultation: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Update consultation status
  Future<void> updateConsultationStatus(ConsultationStatus status) async {
    if (_currentConsultation == null) return;

    try {
      _currentConsultation = _currentConsultation!.copyWith(
        status: status,
        updatedAt: DateTime.now(),
      );

      // Update start time when recording begins
      if (status == ConsultationStatus.recording && _currentConsultation!.startTime == null) {
        _currentConsultation = _currentConsultation!.copyWith(
          startTime: DateTime.now(),
        );
      }

      // Update end time when recording stops
      if (status == ConsultationStatus.processing && _currentConsultation!.endTime == null) {
        final endTime = DateTime.now();
        final duration = _currentConsultation!.startTime != null
            ? endTime.difference(_currentConsultation!.startTime!).inSeconds.toDouble()
            : null;

        _currentConsultation = _currentConsultation!.copyWith(
          endTime: endTime,
          duration: duration,
        );
      }

      notifyListeners();
    } catch (e) {
      _setError('Failed to update consultation status: ${e.toString()}');
    }
  }

  /// Finish consultation
  Future<void> finishConsultation({String? doctorNotes}) async {
    if (_currentConsultationId == null) {
      _setError('No active consultation to finish');
      return;
    }

    try {
      _setLoading(true);
      _clearError();

      // Update status to processing
      await updateConsultationStatus(ConsultationStatus.processing);

      // Call API to finish consultation
      final response = await _apiService.finishConsultation(
        consultationId: _currentConsultationId!,
        doctorNotes: doctorNotes,
      );

      // Update consultation with final data
      if (_currentConsultation != null) {
        _currentConsultation = _currentConsultation!.copyWith(
          status: response.status,
          transcriptSegments: response.transcriptSegments,
          doctorNotes: doctorNotes,
          updatedAt: DateTime.now(),
        );

        // Add to history
        _consultationHistory.insert(0, _currentConsultation!);
      }

      notifyListeners();
    } catch (e) {
      _setError('Failed to finish consultation: ${e.toString()}');
      // Update status to failed
      if (_currentConsultation != null) {
        _currentConsultation = _currentConsultation!.copyWith(
          status: ConsultationStatus.failed,
          updatedAt: DateTime.now(),
        );
        notifyListeners();
      }
    } finally {
      _setLoading(false);
    }
  }

  /// Generate PDF for current consultation
  Future<void> generatePdf({
    String? doctorNotes,
    bool includeCoverPage = true,
    bool includeTranscript = true,
    bool includeNotesPage = true,
  }) async {
    if (_currentConsultationId == null) {
      _setError('No active consultation for PDF generation');
      return;
    }

    try {
      _setLoading(true);
      _clearError();

      final response = await _apiService.generatePdf(
        consultationId: _currentConsultationId!,
        doctorNotes: doctorNotes,
        includeCoverPage: includeCoverPage,
        includeTranscript: includeTranscript,
        includeNotesPage: includeNotesPage,
      );

      // Update consultation with PDF info
      if (_currentConsultation != null) {
        _currentConsultation = _currentConsultation!.copyWith(
          pdfFilePath: response.pdfUrl,
          doctorNotes: doctorNotes ?? _currentConsultation!.doctorNotes,
          updatedAt: DateTime.now(),
        );
        notifyListeners();
      }

    } catch (e) {
      _setError('Failed to generate PDF: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Download PDF for current consultation
  Future<Uint8List?> downloadPdf() async {
    if (_currentConsultationId == null) {
      _setError('No active consultation for PDF download');
      return null;
    }

    try {
      _setLoading(true);
      _clearError();

      final pdfData = await _apiService.downloadPdf(_currentConsultationId!);
      return pdfData;

    } catch (e) {
      _setError('Failed to download PDF: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Load consultation history
  Future<void> loadConsultationHistory() async {
    try {
      _setLoading(true);
      _clearError();

      final consultations = await _apiService.listConsultations();
      
      // Convert to Consultation objects (simplified)
      _consultationHistory = consultations.map((response) {
        return Consultation(
          id: response.consultationId,
          patientInfo: PatientInfo(
            name: response.patientName,
            age: 0, // Would need to fetch full details
            gender: 'Unknown',
            reasonForVisit: 'Unknown',
          ),
          status: response.status,
          doctorName: response.doctorName,
          startTime: response.startTime,
          duration: response.duration,
          createdAt: response.startTime ?? DateTime.now(),
        );
      }).toList();

      notifyListeners();
    } catch (e) {
      _setError('Failed to load consultation history: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Clear current consultation
  void clearCurrentConsultation() {
    _currentConsultation = null;
    _currentConsultationId = null;
    _clearError();
    notifyListeners();
  }

  /// Add doctor notes to current consultation
  void addDoctorNotes(String notes) {
    if (_currentConsultation != null) {
      _currentConsultation = _currentConsultation!.copyWith(
        doctorNotes: notes,
        updatedAt: DateTime.now(),
      );
      notifyListeners();
    }
  }

  /// Get consultation by ID from history
  Consultation? getConsultationById(String id) {
    try {
      return _consultationHistory.firstWhere((consultation) => consultation.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Remove consultation from history
  void removeConsultationFromHistory(String id) {
    _consultationHistory.removeWhere((consultation) => consultation.id == id);
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _error = null;
  }

  /// Dispose resources
  @override
  void dispose() {
    _apiService.dispose();
    super.dispose();
  }
}
