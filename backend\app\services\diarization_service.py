import logging
# import torch
# import torchaudio
from typing import List, Dict, Any, Tu<PERSON>
from pathlib import Path
import numpy as np
from app.models.transcript_segment import SpeakerType

logger = logging.getLogger(__name__)


class DiarizationService:
    """Speaker diarization service using pyannote.audio."""
    
    def __init__(self):
        self.pipeline = None
        self._initialize_pipeline()
    
    def _initialize_pipeline(self):
        """Initialize the diarization pipeline."""
        try:
            # Commented out for demo - requires torch and pyannote-audio
            # from pyannote.audio import Pipeline
            # self.pipeline = Pipeline.from_pretrained("pyannote/speaker-diarization-3.1")
            logger.warning("Diarization pipeline not available - using simple speaker detection")
            self.pipeline = None

        except Exception as e:
            logger.warning(f"Could not initialize diarization pipeline: {e}")
            logger.info("Falling back to simple speaker detection")
            self.pipeline = None
    
    async def diarize_audio(self, audio_file_path: str) -> List[Dict[str, Any]]:
        """
        Perform speaker diarization on audio file.
        
        Args:
            audio_file_path: Path to audio file
            
        Returns:
            List of speaker segments with timestamps
        """
        try:
            if self.pipeline is None:
                return await self._simple_speaker_detection(audio_file_path)
            
            # Load audio file (commented out - requires torchaudio)
            # waveform, sample_rate = torchaudio.load(audio_file_path)
            
            # Apply diarization
            diarization = self.pipeline({"waveform": waveform, "sample_rate": sample_rate})
            
            # Convert to our format
            segments = []
            for turn, _, speaker in diarization.itertracks(yield_label=True):
                segments.append({
                    "start_time": turn.start,
                    "end_time": turn.end,
                    "speaker_id": speaker,
                    "speaker_type": self._classify_speaker(speaker),
                    "confidence": 0.8  # Default confidence
                })
            
            return segments
            
        except Exception as e:
            logger.error(f"Error in diarization: {e}")
            return await self._simple_speaker_detection(audio_file_path)
    
    async def _simple_speaker_detection(self, audio_file_path: str) -> List[Dict[str, Any]]:
        """
        Simple fallback speaker detection based on audio characteristics.
        This is a placeholder implementation.
        """
        try:
            # Simulate audio duration (in real implementation, would load audio file)
            duration = 60.0  # Default 1 minute for demo
            
            # Simple heuristic: alternate between doctor and patient every 5-10 seconds
            segments = []
            current_time = 0.0
            is_doctor = True
            
            while current_time < duration:
                segment_duration = min(np.random.uniform(3, 8), duration - current_time)
                
                segments.append({
                    "start_time": current_time,
                    "end_time": current_time + segment_duration,
                    "speaker_id": "doctor" if is_doctor else "patient",
                    "speaker_type": SpeakerType.DOCTOR if is_doctor else SpeakerType.PATIENT,
                    "confidence": 0.6
                })
                
                current_time += segment_duration
                is_doctor = not is_doctor
            
            return segments
            
        except Exception as e:
            logger.error(f"Error in simple speaker detection: {e}")
            return []
    
    def _classify_speaker(self, speaker_id: str) -> SpeakerType:
        """
        Classify speaker ID into doctor/patient/other.
        This is a simple heuristic - in practice, you'd train a classifier.
        """
        # Simple heuristic based on speaker patterns
        if "SPEAKER_00" in speaker_id or "0" in speaker_id:
            return SpeakerType.DOCTOR
        elif "SPEAKER_01" in speaker_id or "1" in speaker_id:
            return SpeakerType.PATIENT
        else:
            return SpeakerType.OTHER
    
    def filter_speakers(
        self, 
        segments: List[Dict[str, Any]], 
        keep_speakers: List[SpeakerType] = None
    ) -> List[Dict[str, Any]]:
        """
        Filter segments to keep only specified speakers.
        
        Args:
            segments: List of speaker segments
            keep_speakers: List of speaker types to keep (default: doctor and patient)
            
        Returns:
            Filtered segments
        """
        if keep_speakers is None:
            keep_speakers = [SpeakerType.DOCTOR, SpeakerType.PATIENT]
        
        filtered_segments = []
        for segment in segments:
            speaker_type = segment.get("speaker_type", SpeakerType.UNKNOWN)
            if speaker_type in keep_speakers:
                filtered_segments.append(segment)
        
        return filtered_segments
    
    def merge_segments_with_transcript(
        self, 
        diarization_segments: List[Dict[str, Any]], 
        transcript_segments: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Merge diarization results with transcript segments.
        
        Args:
            diarization_segments: Speaker diarization results
            transcript_segments: Transcription results
            
        Returns:
            Merged segments with speaker labels and text
        """
        merged_segments = []
        
        for transcript_seg in transcript_segments:
            transcript_start = transcript_seg.get("start_time", 0)
            transcript_end = transcript_seg.get("end_time", 0)
            transcript_text = transcript_seg.get("text", "")
            
            # Find overlapping diarization segment
            best_overlap = 0
            best_speaker = SpeakerType.UNKNOWN
            
            for diar_seg in diarization_segments:
                diar_start = diar_seg.get("start_time", 0)
                diar_end = diar_seg.get("end_time", 0)
                
                # Calculate overlap
                overlap_start = max(transcript_start, diar_start)
                overlap_end = min(transcript_end, diar_end)
                overlap = max(0, overlap_end - overlap_start)
                
                if overlap > best_overlap:
                    best_overlap = overlap
                    best_speaker = diar_seg.get("speaker_type", SpeakerType.UNKNOWN)
            
            merged_segments.append({
                "start_time": transcript_start,
                "end_time": transcript_end,
                "speaker": best_speaker,
                "text": transcript_text,
                "confidence": transcript_seg.get("confidence", 0.8)
            })
        
        return merged_segments


# Global diarization service instance
diarization_service = DiarizationService()
