from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import logging
import uvicorn
from datetime import datetime

from app.core.config import settings
from app.api.routers import consultation_router, audio_router, pdf_router
from app.api.schemas import HealthCheckResponse, ErrorResponse

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create FastAPI application
app = FastAPI(
    title="DocTranscribe API",
    description="Medical consultation transcription and PDF generation service",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(consultation_router.router, prefix="/api/v1")
app.include_router(audio_router.router, prefix="/api/v1")
app.include_router(pdf_router.router, prefix="/api/v1")


@app.get("/", response_model=HealthCheckResponse)
async def root():
    """Root endpoint with basic API information."""
    return HealthCheckResponse(
        status="healthy",
        timestamp=datetime.utcnow(),
        version="1.0.0",
        services={
            "riva": "connected" if settings.nvidia_api_key else "not configured",
            "pdf_generator": "available",
            "diarization": "available"
        }
    )


@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """Health check endpoint."""
    try:
        # Check if required services are available
        services_status = {}
        
        # Check NVIDIA API key
        if settings.nvidia_api_key:
            services_status["riva"] = "configured"
        else:
            services_status["riva"] = "not configured"
        
        # Check PDF output directory
        import os
        if os.path.exists(settings.pdf_output_dir):
            services_status["pdf_storage"] = "available"
        else:
            services_status["pdf_storage"] = "directory not found"
        
        # Check temp audio directory
        if os.path.exists(settings.temp_audio_dir):
            services_status["audio_storage"] = "available"
        else:
            services_status["audio_storage"] = "directory not found"
        
        return HealthCheckResponse(
            status="healthy",
            timestamp=datetime.utcnow(),
            version="1.0.0",
            services=services_status
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service unhealthy: {str(e)}"
        )


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Global HTTP exception handler."""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.detail,
            detail=getattr(exc, 'detail', None),
            timestamp=datetime.utcnow()
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Global exception handler for unhandled exceptions."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="Internal server error",
            detail=str(exc) if settings.debug else "An unexpected error occurred",
            timestamp=datetime.utcnow()
        ).dict()
    )


@app.on_event("startup")
async def startup_event():
    """Application startup event."""
    logger.info("Starting DocTranscribe API...")
    logger.info(f"Debug mode: {settings.debug}")
    logger.info(f"NVIDIA API configured: {'Yes' if settings.nvidia_api_key else 'No'}")
    logger.info(f"PDF output directory: {settings.pdf_output_dir}")
    logger.info(f"Temp audio directory: {settings.temp_audio_dir}")
    
    # Create necessary directories
    import os
    os.makedirs(settings.pdf_output_dir, exist_ok=True)
    os.makedirs(settings.temp_audio_dir, exist_ok=True)
    
    logger.info("DocTranscribe API started successfully")


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event."""
    logger.info("Shutting down DocTranscribe API...")
    
    # Clean up resources
    try:
        from .services.riva_client import riva_client
        await riva_client.close()
        logger.info("Closed NVIDIA Riva client")
    except Exception as e:
        logger.error(f"Error closing Riva client: {e}")
    
    # Clean up temporary files
    try:
        from .core.utils import cleanup_temp_files
        cleanup_temp_files(settings.temp_audio_dir)
        logger.info("Cleaned up temporary files")
    except Exception as e:
        logger.error(f"Error cleaning up temp files: {e}")
    
    logger.info("DocTranscribe API shutdown complete")


# Additional endpoints for development/debugging
if settings.debug:
    @app.get("/debug/consultations")
    async def debug_list_all_consultations():
        """Debug endpoint to list all consultations."""
        from .api.dependencies import get_all_consultations
        return get_all_consultations()
    
    @app.get("/debug/config")
    async def debug_get_config():
        """Debug endpoint to get configuration (without secrets)."""
        return {
            "api_host": settings.api_host,
            "api_port": settings.api_port,
            "debug": settings.debug,
            "audio_sample_rate": settings.audio_sample_rate,
            "audio_channels": settings.audio_channels,
            "max_audio_chunk_size": settings.max_audio_chunk_size,
            "pdf_output_dir": settings.pdf_output_dir,
            "temp_audio_dir": settings.temp_audio_dir,
            "log_level": settings.log_level,
            "riva_function_id": settings.riva_function_id,
            "has_nvidia_api_key": bool(settings.nvidia_api_key)
        }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
