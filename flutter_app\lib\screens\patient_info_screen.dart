import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../models/patient_info.dart';
import '../providers/consultation_provider.dart';
import '../utils/constants.dart';

class PatientInfoScreen extends StatefulWidget {
  const PatientInfoScreen({super.key});

  @override
  State<PatientInfoScreen> createState() => _PatientInfoScreenState();
}

class _PatientInfoScreenState extends State<PatientInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _mrnController = TextEditingController();
  final _reasonController = TextEditingController();
  final _doctorNameController = TextEditingController();

  String _selectedGender = Gender.male.displayName;
  String _selectedLanguage = AppConstants.defaultLanguage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSavedDoctorName();
  }

  void _loadSavedDoctorName() {
    // In a real app, load from SharedPreferences
    _doctorNameController.text = AppConstants.defaultDoctorName;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Patient Information'),
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => context.go('/history'),
            tooltip: 'View History',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => context.go('/settings'),
            tooltip: 'Settings',
          ),
        ],
      ),
      body: Consumer<ConsultationProvider>(
        builder: (context, consultationProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Icon(
                            Icons.medical_information,
                            size: 48,
                            color: AppColors.primary,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'DocTranscribe',
                            style: AppTextStyles.headline2,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Medical Consultation Transcription',
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Doctor Information
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Doctor Information',
                            style: AppTextStyles.headline3,
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _doctorNameController,
                            decoration: const InputDecoration(
                              labelText: 'Doctor Name',
                              prefixIcon: Icon(Icons.person),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Doctor name is required';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          DropdownButtonFormField<String>(
                            value: _selectedLanguage,
                            decoration: const InputDecoration(
                              labelText: 'Transcription Language',
                              prefixIcon: Icon(Icons.language),
                            ),
                            items: AppConstants.supportedLanguages.entries
                                .map((entry) => DropdownMenuItem(
                                      value: entry.key,
                                      child: Text(entry.value),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedLanguage = value;
                                });
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Patient Information
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Patient Information',
                            style: AppTextStyles.headline3,
                          ),
                          const SizedBox(height: 16),
                          
                          // Patient Name
                          TextFormField(
                            controller: _nameController,
                            decoration: const InputDecoration(
                              labelText: 'Patient Name *',
                              prefixIcon: Icon(Icons.person_outline),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Patient name is required';
                              }
                              if (value.length > AppConstants.maxPatientNameLength) {
                                return 'Name too long (max ${AppConstants.maxPatientNameLength} characters)';
                              }
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Age and Gender Row
                          Row(
                            children: [
                              Expanded(
                                flex: 1,
                                child: TextFormField(
                                  controller: _ageController,
                                  decoration: const InputDecoration(
                                    labelText: 'Age *',
                                    prefixIcon: Icon(Icons.cake),
                                  ),
                                  keyboardType: TextInputType.number,
                                  validator: (value) {
                                    if (value == null || value.trim().isEmpty) {
                                      return 'Age is required';
                                    }
                                    final age = int.tryParse(value);
                                    if (age == null) {
                                      return 'Invalid age';
                                    }
                                    if (age < AppConstants.minAge || age > AppConstants.maxAge) {
                                      return 'Age must be between ${AppConstants.minAge} and ${AppConstants.maxAge}';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                flex: 2,
                                child: DropdownButtonFormField<String>(
                                  value: _selectedGender,
                                  decoration: const InputDecoration(
                                    labelText: 'Gender *',
                                    prefixIcon: Icon(Icons.wc),
                                  ),
                                  items: Gender.values
                                      .map((gender) => DropdownMenuItem(
                                            value: gender.displayName,
                                            child: Text(gender.displayName),
                                          ))
                                      .toList(),
                                  onChanged: (value) {
                                    if (value != null) {
                                      setState(() {
                                        _selectedGender = value;
                                      });
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // MRN
                          TextFormField(
                            controller: _mrnController,
                            decoration: const InputDecoration(
                              labelText: 'Medical Record Number (MRN)',
                              prefixIcon: Icon(Icons.badge),
                              helperText: 'Optional',
                            ),
                            validator: (value) {
                              if (value != null && value.isNotEmpty && value.length > 50) {
                                return 'MRN too long (max 50 characters)';
                              }
                              return null;
                            },
                          ),
                          
                          const SizedBox(height: 16),
                          
                          // Reason for Visit
                          TextFormField(
                            controller: _reasonController,
                            decoration: const InputDecoration(
                              labelText: 'Reason for Visit *',
                              prefixIcon: Icon(Icons.medical_services),
                              helperText: 'Brief description of the consultation purpose',
                            ),
                            maxLines: 3,
                            maxLength: AppConstants.maxReasonLength,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Reason for visit is required';
                              }
                              if (value.length > AppConstants.maxReasonLength) {
                                return 'Reason too long (max ${AppConstants.maxReasonLength} characters)';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Error Display
                  if (consultationProvider.error != null)
                    Card(
                      color: AppColors.error.withOpacity(0.1),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Icon(Icons.error, color: AppColors.error),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                consultationProvider.error!,
                                style: TextStyle(color: AppColors.error),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  
                  if (consultationProvider.error != null)
                    const SizedBox(height: 16),
                  
                  // Start Consultation Button
                  ElevatedButton(
                    onPressed: _isLoading || consultationProvider.isLoading
                        ? null
                        : _startConsultation,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      textStyle: AppTextStyles.button,
                    ),
                    child: _isLoading || consultationProvider.isLoading
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              ),
                              SizedBox(width: 8),
                              Text('Starting Consultation...'),
                            ],
                          )
                        : const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.play_arrow),
                              SizedBox(width: 8),
                              Text('Start Consultation'),
                            ],
                          ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _startConsultation() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final patientInfo = PatientInfo(
        name: _nameController.text.trim(),
        age: int.parse(_ageController.text.trim()),
        gender: _selectedGender,
        mrn: _mrnController.text.trim().isEmpty ? null : _mrnController.text.trim(),
        reasonForVisit: _reasonController.text.trim(),
      );

      final consultationProvider = context.read<ConsultationProvider>();
      
      await consultationProvider.startConsultation(
        patientInfo: patientInfo,
        doctorName: _doctorNameController.text.trim(),
        language: _selectedLanguage,
      );

      if (consultationProvider.error == null) {
        // Navigate to recording screen
        if (mounted) {
          context.go('/recording');
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start consultation: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    _mrnController.dispose();
    _reasonController.dispose();
    _doctorNameController.dispose();
    super.dispose();
  }
}
