/// Speaker types for transcript segments
enum SpeakerType {
  doctor('doctor'),
  patient('patient'),
  other('other'),
  unknown('unknown');

  const SpeakerType(this.value);
  final String value;

  static SpeakerType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'doctor':
        return SpeakerType.doctor;
      case 'patient':
        return SpeakerType.patient;
      case 'other':
        return SpeakerType.other;
      default:
        return SpeakerType.unknown;
    }
  }

  String get displayName {
    switch (this) {
      case SpeakerType.doctor:
        return 'Doctor';
      case SpeakerType.patient:
        return 'Patient';
      case SpeakerType.other:
        return 'Other';
      case SpeakerType.unknown:
        return 'Unknown';
    }
  }
}

/// Individual transcript segment model
class TranscriptSegment {
  final String? id;
  final String consultationId;
  final double startTime;
  final double endTime;
  final SpeakerType speaker;
  final String text;
  final double? confidence;
  final bool isFinal;
  final DateTime? timestamp;

  const TranscriptSegment({
    this.id,
    required this.consultationId,
    required this.startTime,
    required this.endTime,
    required this.speaker,
    required this.text,
    this.confidence,
    this.isFinal = true,
    this.timestamp,
  });

  /// Create TranscriptSegment from JSON
  factory TranscriptSegment.fromJson(Map<String, dynamic> json) {
    return TranscriptSegment(
      id: json['id'] as String?,
      consultationId: json['consultation_id'] as String,
      startTime: (json['start_time'] as num).toDouble(),
      endTime: (json['end_time'] as num).toDouble(),
      speaker: SpeakerType.fromString(json['speaker'] as String),
      text: json['text'] as String,
      confidence: json['confidence'] != null
          ? (json['confidence'] as num).toDouble()
          : null,
      isFinal: json['is_final'] as bool? ?? true,
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'] as String)
          : null,
    );
  }

  /// Convert TranscriptSegment to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'consultation_id': consultationId,
      'start_time': startTime,
      'end_time': endTime,
      'speaker': speaker.value,
      'text': text,
      'confidence': confidence,
      'is_final': isFinal,
      'timestamp': timestamp?.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  TranscriptSegment copyWith({
    String? id,
    String? consultationId,
    double? startTime,
    double? endTime,
    SpeakerType? speaker,
    String? text,
    double? confidence,
    bool? isFinal,
    DateTime? timestamp,
  }) {
    return TranscriptSegment(
      id: id ?? this.id,
      consultationId: consultationId ?? this.consultationId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      speaker: speaker ?? this.speaker,
      text: text ?? this.text,
      confidence: confidence ?? this.confidence,
      isFinal: isFinal ?? this.isFinal,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  /// Get duration of the segment in seconds
  double get duration => endTime - startTime;

  /// Get formatted time range
  String get timeRange {
    return '${_formatTime(startTime)} - ${_formatTime(endTime)}';
  }

  /// Get formatted start time
  String get formattedStartTime => _formatTime(startTime);

  /// Get formatted end time
  String get formattedEndTime => _formatTime(endTime);

  /// Get word count
  int get wordCount => text.trim().split(RegExp(r'\s+')).length;

  /// Check if segment is valid
  bool get isValid {
    return consultationId.isNotEmpty &&
        startTime >= 0 &&
        endTime > startTime &&
        text.trim().isNotEmpty;
  }

  /// Format time in MM:SS or HH:MM:SS format
  String _formatTime(double seconds) {
    final duration = Duration(milliseconds: (seconds * 1000).round());
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${secs.toString().padLeft(2, '0')}';
    }
  }

  @override
  String toString() {
    return 'TranscriptSegment(id: $id, speaker: ${speaker.displayName}, '
        'time: $timeRange, text: ${text.substring(0, text.length > 50 ? 50 : text.length)}...)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TranscriptSegment &&
        other.id == id &&
        other.consultationId == consultationId &&
        other.startTime == startTime &&
        other.endTime == endTime &&
        other.speaker == speaker &&
        other.text == text &&
        other.confidence == confidence &&
        other.isFinal == isFinal;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      consultationId,
      startTime,
      endTime,
      speaker,
      text,
      confidence,
      isFinal,
    );
  }
}

/// Transcript summary statistics
class TranscriptStats {
  final double totalDuration;
  final int wordCount;
  final Map<SpeakerType, SpeakerStats> speakerStats;
  final int segmentCount;
  final DateTime createdAt;

  const TranscriptStats({
    required this.totalDuration,
    required this.wordCount,
    required this.speakerStats,
    required this.segmentCount,
    required this.createdAt,
  });

  factory TranscriptStats.fromJson(Map<String, dynamic> json) {
    final speakerStatsMap = <SpeakerType, SpeakerStats>{};
    final speakerStatsJson = json['speaker_stats'] as Map<String, dynamic>;

    for (final entry in speakerStatsJson.entries) {
      final speakerType = SpeakerType.fromString(entry.key);
      final stats = SpeakerStats.fromJson(entry.value as Map<String, dynamic>);
      speakerStatsMap[speakerType] = stats;
    }

    return TranscriptStats(
      totalDuration: (json['total_duration'] as num).toDouble(),
      wordCount: json['word_count'] as int,
      speakerStats: speakerStatsMap,
      segmentCount: json['segment_count'] as int,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    final speakerStatsJson = <String, dynamic>{};
    for (final entry in speakerStats.entries) {
      speakerStatsJson[entry.key.value] = entry.value.toJson();
    }

    return {
      'total_duration': totalDuration,
      'word_count': wordCount,
      'speaker_stats': speakerStatsJson,
      'segment_count': segmentCount,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

/// Statistics for individual speakers
class SpeakerStats {
  final double duration;
  final int wordCount;
  final int segmentCount;

  const SpeakerStats({
    required this.duration,
    required this.wordCount,
    required this.segmentCount,
  });

  factory SpeakerStats.fromJson(Map<String, dynamic> json) {
    return SpeakerStats(
      duration: (json['duration'] as num).toDouble(),
      wordCount: json['word_count'] as int,
      segmentCount: json['segment_count'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'duration': duration,
      'word_count': wordCount,
      'segment_count': segmentCount,
    };
  }

  /// Get percentage of total duration
  double getPercentageOfTotal(double totalDuration) {
    if (totalDuration == 0) return 0;
    return (duration / totalDuration) * 100;
  }
}
