import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../providers/consultation_provider.dart';
import '../providers/transcript_provider.dart';
import '../utils/constants.dart';

class RecordingScreen extends StatefulWidget {
  const RecordingScreen({super.key});

  @override
  State<RecordingScreen> createState() => _RecordingScreenState();
}

class _RecordingScreenState extends State<RecordingScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  bool _isRecording = false;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Recording'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => _showExitDialog(context),
        ),
      ),
      body: Consumer2<ConsultationProvider, TranscriptProvider>(
        builder: (context, consultationProvider, transcriptProvider, child) {
          final consultation = consultationProvider.currentConsultation;
          
          if (consultation == null) {
            return const Center(
              child: Text('No active consultation'),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: [
                // Patient Info Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: AppColors.primary,
                          child: Text(
                            consultation.patientInfo.name.isNotEmpty
                                ? consultation.patientInfo.name[0].toUpperCase()
                                : 'P',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                consultation.patientInfo.name,
                                style: AppTextStyles.headline3,
                              ),
                              Text(
                                '${consultation.patientInfo.age} years old, ${consultation.patientInfo.gender}',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              if (consultation.patientInfo.mrn != null)
                                Text(
                                  'MRN: ${consultation.patientInfo.mrn}',
                                  style: AppTextStyles.bodySmall,
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Recording Status
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Recording Button
                      GestureDetector(
                        onTap: _toggleRecording,
                        child: AnimatedBuilder(
                          animation: _pulseController,
                          builder: (context, child) {
                            return Container(
                              width: 200,
                              height: 200,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: _isRecording
                                    ? AppColors.recordingActive
                                    : AppColors.recordingInactive,
                                boxShadow: _isRecording
                                    ? [
                                        BoxShadow(
                                          color: AppColors.recordingActive
                                              .withOpacity(0.3),
                                          blurRadius: 20 * _pulseController.value,
                                          spreadRadius: 10 * _pulseController.value,
                                        ),
                                      ]
                                    : null,
                              ),
                              child: Icon(
                                _isRecording ? Icons.stop : Icons.mic,
                                size: 80,
                                color: Colors.white,
                              ),
                            );
                          },
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Recording Status Text
                      Text(
                        _isRecording ? 'Recording...' : 'Tap to Start Recording',
                        style: AppTextStyles.headline3.copyWith(
                          color: _isRecording
                              ? AppColors.recordingActive
                              : AppColors.textSecondary,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Duration Display
                      Text(
                        '00:00', // Would show actual duration
                        style: AppTextStyles.headline2.copyWith(
                          fontFamily: 'monospace',
                        ),
                      ),

                      const SizedBox(height: 32),

                      // Live Transcript Preview
                      if (transcriptProvider.liveTranscript.isNotEmpty)
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Live Transcript',
                                  style: AppTextStyles.bodyMedium.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  transcriptProvider.liveTranscript,
                                  style: AppTextStyles.bodyMedium,
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                // Control Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Pause/Resume Button
                    if (_isRecording)
                      ElevatedButton.icon(
                        onPressed: () {
                          // Implement pause/resume
                        },
                        icon: const Icon(Icons.pause),
                        label: const Text('Pause'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.warning,
                        ),
                      ),

                    // Finish Button
                    if (_isRecording)
                      ElevatedButton.icon(
                        onPressed: _finishRecording,
                        icon: const Icon(Icons.check),
                        label: const Text('Finish'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.success,
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 16),

                // Error Display
                if (transcriptProvider.error != null)
                  Card(
                    color: AppColors.error.withOpacity(0.1),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Icon(Icons.error, color: AppColors.error),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              transcriptProvider.error!,
                              style: TextStyle(color: AppColors.error),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _toggleRecording() async {
    final consultationProvider = context.read<ConsultationProvider>();
    final transcriptProvider = context.read<TranscriptProvider>();

    if (!_isRecording) {
      // Start recording
      setState(() {
        _isRecording = true;
      });

      _pulseController.repeat();
      _waveController.repeat();

      // Update consultation status
      await consultationProvider.updateConsultationStatus(
        ConsultationStatus.recording,
      );

      // Start transcription
      if (consultationProvider.currentConsultationId != null) {
        await transcriptProvider.startTranscription(
          consultationProvider.currentConsultationId!,
        );
      }
    } else {
      // Stop recording
      await _stopRecording();
    }
  }

  Future<void> _stopRecording() async {
    final transcriptProvider = context.read<TranscriptProvider>();

    setState(() {
      _isRecording = false;
    });

    _pulseController.stop();
    _waveController.stop();

    // Stop transcription
    await transcriptProvider.stopTranscription();
  }

  Future<void> _finishRecording() async {
    await _stopRecording();

    final consultationProvider = context.read<ConsultationProvider>();

    // Show loading dialog
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Processing consultation...'),
            ],
          ),
        ),
      );
    }

    try {
      // Finish consultation
      await consultationProvider.finishConsultation();

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        context.go('/review');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to finish consultation: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _showExitDialog(BuildContext context) async {
    if (!_isRecording) {
      context.go('/');
      return;
    }

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit Recording'),
        content: const Text(
          'Are you sure you want to exit? The current recording will be lost.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Exit'),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
          ),
        ],
      ),
    );

    if (result == true && mounted) {
      await _stopRecording();
      if (mounted) {
        context.go('/');
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }
}
