import 'patient_info.dart';
import 'transcript_segment.dart';

/// Consultation status enumeration
enum ConsultationStatus {
  created('created'),
  recording('recording'),
  processing('processing'),
  completed('completed'),
  failed('failed');

  const ConsultationStatus(this.value);
  final String value;

  static ConsultationStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'created':
        return ConsultationStatus.created;
      case 'recording':
        return ConsultationStatus.recording;
      case 'processing':
        return ConsultationStatus.processing;
      case 'completed':
        return ConsultationStatus.completed;
      case 'failed':
        return ConsultationStatus.failed;
      default:
        return ConsultationStatus.created;
    }
  }

  String get displayName {
    switch (this) {
      case ConsultationStatus.created:
        return 'Created';
      case ConsultationStatus.recording:
        return 'Recording';
      case ConsultationStatus.processing:
        return 'Processing';
      case ConsultationStatus.completed:
        return 'Completed';
      case ConsultationStatus.failed:
        return 'Failed';
    }
  }
}

/// Main consultation model
class Consultation {
  final String id;
  final PatientInfo patientInfo;
  final ConsultationStatus status;
  final String? doctorName;
  final DateTime? startTime;
  final DateTime? endTime;
  final double? duration;
  final String? audioFilePath;
  final List<TranscriptSegment> transcriptSegments;
  final String? doctorNotes;
  final String? pdfFilePath;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? language;

  const Consultation({
    required this.id,
    required this.patientInfo,
    required this.status,
    this.doctorName,
    this.startTime,
    this.endTime,
    this.duration,
    this.audioFilePath,
    this.transcriptSegments = const [],
    this.doctorNotes,
    this.pdfFilePath,
    required this.createdAt,
    this.updatedAt,
    this.language,
  });

  /// Create Consultation from JSON
  factory Consultation.fromJson(Map<String, dynamic> json) {
    final transcriptSegmentsList = json['transcript_segments'] as List<dynamic>? ?? [];
    final transcriptSegments = transcriptSegmentsList
        .map((segment) => TranscriptSegment.fromJson(segment as Map<String, dynamic>))
        .toList();

    return Consultation(
      id: json['id'] as String,
      patientInfo: PatientInfo.fromJson(json['patient_info'] as Map<String, dynamic>),
      status: ConsultationStatus.fromString(json['status'] as String),
      doctorName: json['doctor_name'] as String?,
      startTime: json['start_time'] != null
          ? DateTime.parse(json['start_time'] as String)
          : null,
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'] as String)
          : null,
      duration: json['duration'] != null
          ? (json['duration'] as num).toDouble()
          : null,
      audioFilePath: json['audio_file_path'] as String?,
      transcriptSegments: transcriptSegments,
      doctorNotes: json['doctor_notes'] as String?,
      pdfFilePath: json['pdf_file_path'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      language: json['language'] as String?,
    );
  }

  /// Convert Consultation to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_info': patientInfo.toJson(),
      'status': status.value,
      'doctor_name': doctorName,
      'start_time': startTime?.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'duration': duration,
      'audio_file_path': audioFilePath,
      'transcript_segments': transcriptSegments.map((segment) => segment.toJson()).toList(),
      'doctor_notes': doctorNotes,
      'pdf_file_path': pdfFilePath,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'language': language,
    };
  }

  /// Create a copy with updated fields
  Consultation copyWith({
    String? id,
    PatientInfo? patientInfo,
    ConsultationStatus? status,
    String? doctorName,
    DateTime? startTime,
    DateTime? endTime,
    double? duration,
    String? audioFilePath,
    List<TranscriptSegment>? transcriptSegments,
    String? doctorNotes,
    String? pdfFilePath,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? language,
  }) {
    return Consultation(
      id: id ?? this.id,
      patientInfo: patientInfo ?? this.patientInfo,
      status: status ?? this.status,
      doctorName: doctorName ?? this.doctorName,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      audioFilePath: audioFilePath ?? this.audioFilePath,
      transcriptSegments: transcriptSegments ?? this.transcriptSegments,
      doctorNotes: doctorNotes ?? this.doctorNotes,
      pdfFilePath: pdfFilePath ?? this.pdfFilePath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      language: language ?? this.language,
    );
  }

  /// Get formatted duration
  String get formattedDuration {
    if (duration == null) return 'Unknown';
    
    final dur = Duration(seconds: duration!.round());
    final hours = dur.inHours;
    final minutes = dur.inMinutes.remainder(60);
    final seconds = dur.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Get formatted date
  String get formattedDate {
    if (startTime == null) return 'Unknown';
    
    final date = startTime!;
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Get formatted time
  String get formattedTime {
    if (startTime == null) return 'Unknown';
    
    final time = startTime!;
    return '${time.hour.toString().padLeft(2, '0')}:'
           '${time.minute.toString().padLeft(2, '0')}';
  }

  /// Get total word count from transcript
  int get totalWordCount {
    return transcriptSegments.fold(0, (sum, segment) => sum + segment.wordCount);
  }

  /// Check if consultation has transcript
  bool get hasTranscript => transcriptSegments.isNotEmpty;

  /// Check if consultation has PDF
  bool get hasPdf => pdfFilePath != null && pdfFilePath!.isNotEmpty;

  /// Check if consultation is in progress
  bool get isInProgress {
    return status == ConsultationStatus.recording ||
           status == ConsultationStatus.processing;
  }

  /// Check if consultation is completed
  bool get isCompleted => status == ConsultationStatus.completed;

  /// Check if consultation has failed
  bool get hasFailed => status == ConsultationStatus.failed;

  /// Get transcript segments by speaker
  List<TranscriptSegment> getSegmentsBySpeaker(SpeakerType speaker) {
    return transcriptSegments.where((segment) => segment.speaker == speaker).toList();
  }

  /// Get doctor segments
  List<TranscriptSegment> get doctorSegments {
    return getSegmentsBySpeaker(SpeakerType.doctor);
  }

  /// Get patient segments
  List<TranscriptSegment> get patientSegments {
    return getSegmentsBySpeaker(SpeakerType.patient);
  }

  /// Calculate speaking time for each speaker
  Map<SpeakerType, double> get speakingTime {
    final speakingTime = <SpeakerType, double>{};
    
    for (final segment in transcriptSegments) {
      speakingTime[segment.speaker] = 
          (speakingTime[segment.speaker] ?? 0) + segment.duration;
    }
    
    return speakingTime;
  }

  @override
  String toString() {
    return 'Consultation(id: $id, patient: ${patientInfo.name}, '
           'status: ${status.displayName}, duration: $formattedDuration)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Consultation &&
        other.id == id &&
        other.patientInfo == patientInfo &&
        other.status == status &&
        other.doctorName == doctorName &&
        other.startTime == startTime &&
        other.endTime == endTime &&
        other.duration == duration;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      patientInfo,
      status,
      doctorName,
      startTime,
      endTime,
      duration,
    );
  }
}
