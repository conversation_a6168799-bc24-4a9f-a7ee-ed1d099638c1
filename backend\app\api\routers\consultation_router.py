from fastapi import APIRouter, HTTPException, Depends, status
from typing import List, Optional
import logging
from datetime import datetime

from ..schemas import (
    StartConsultationRequest, StartConsultationResponse,
    FinishConsultationRequest, FinishConsultationResponse,
    ConsultationStatusResponse, ConsultationListParams,
    ErrorResponse
)
from ..dependencies import (
    create_consultation, get_valid_consultation, update_consultation,
    get_consultation, get_all_consultations, cleanup_consultation
)
from ...services.transcript_processing import transcript_processor
from ...services.diarization_service import diarization_service
from ...core.utils import format_duration

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/consultations", tags=["consultations"])


@router.post("/start", response_model=StartConsultationResponse)
async def start_consultation(request: StartConsultationRequest):
    """
    Start a new consultation session.
    
    Creates a new consultation with patient information and returns a consultation ID
    that will be used for all subsequent operations.
    """
    try:
        # Create consultation
        consultation_id = create_consultation(
            patient_info=request.patient_info.dict(),
            doctor_name=request.doctor_name
        )
        
        # Update with start time and status
        update_consultation(consultation_id, {
            "status": "created",
            "start_time": datetime.utcnow(),
            "language": request.language
        })
        
        logger.info(f"Started consultation {consultation_id}")
        
        return StartConsultationResponse(
            consultation_id=consultation_id,
            status="created",
            message="Consultation started successfully"
        )
        
    except Exception as e:
        logger.error(f"Error starting consultation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start consultation: {str(e)}"
        )


@router.post("/finish", response_model=FinishConsultationResponse)
async def finish_consultation(
    request: FinishConsultationRequest,
    consultation: dict = Depends(get_valid_consultation)
):
    """
    Finish a consultation session.
    
    Processes the recorded audio, performs speaker diarization, cleans the transcript,
    and returns the final transcript segments.
    """
    try:
        consultation_id = consultation["id"]
        start_time = datetime.utcnow()
        
        logger.info(f"Finishing consultation {consultation_id}")
        
        # Update status to processing
        update_consultation(consultation_id, {
            "status": "processing",
            "end_time": datetime.utcnow()
        })
        
        # Get transcript segments from consultation
        raw_segments = consultation.get("transcript_segments", [])
        
        if not raw_segments:
            logger.warning(f"No transcript segments found for consultation {consultation_id}")
            processed_segments = []
        else:
            # Process transcript segments
            processed_segments = transcript_processor.process_transcript_segments(
                raw_segments, consultation_id
            )
            
            # Convert to dict format for response
            processed_segments = [seg.dict() for seg in processed_segments]
        
        # Generate summary statistics
        summary_stats = transcript_processor.generate_summary_stats(
            [transcript_processor.TranscriptSegment(**seg) for seg in processed_segments]
        )
        
        # Calculate duration
        duration = None
        if consultation.get("start_time") and consultation.get("end_time"):
            duration = (consultation["end_time"] - consultation["start_time"]).total_seconds()
        
        # Update consultation with final data
        update_consultation(consultation_id, {
            "status": "completed",
            "duration": duration,
            "transcript_segments": processed_segments,
            "doctor_notes": request.doctor_notes
        })
        
        processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        logger.info(f"Finished consultation {consultation_id} in {processing_time:.2f}ms")
        
        return FinishConsultationResponse(
            consultation_id=consultation_id,
            status="completed",
            transcript_segments=processed_segments,
            summary_stats=summary_stats,
            processing_time_ms=processing_time,
            message="Consultation finished successfully"
        )
        
    except Exception as e:
        logger.error(f"Error finishing consultation: {e}")
        
        # Update status to failed
        update_consultation(consultation["id"], {"status": "failed"})
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to finish consultation: {str(e)}"
        )


@router.get("/{consultation_id}/status", response_model=ConsultationStatusResponse)
async def get_consultation_status(consultation_id: str):
    """
    Get the current status of a consultation.
    """
    try:
        consultation = get_consultation(consultation_id)
        if not consultation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Consultation {consultation_id} not found"
            )
        
        patient_info = consultation.get("patient_info", {})
        transcript_segments = consultation.get("transcript_segments", [])
        
        return ConsultationStatusResponse(
            consultation_id=consultation_id,
            status=consultation.get("status", "unknown"),
            patient_name=patient_info.get("name", "Unknown"),
            doctor_name=consultation.get("doctor_name"),
            start_time=consultation.get("start_time"),
            duration=consultation.get("duration"),
            transcript_count=len(transcript_segments),
            has_pdf=bool(consultation.get("pdf_file_path"))
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting consultation status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get consultation status: {str(e)}"
        )


@router.get("/", response_model=List[ConsultationStatusResponse])
async def list_consultations(params: ConsultationListParams = Depends()):
    """
    List all consultations with optional filtering.
    """
    try:
        all_consultations = get_all_consultations()
        consultation_list = []
        
        for consultation_id, consultation in all_consultations.items():
            patient_info = consultation.get("patient_info", {})
            transcript_segments = consultation.get("transcript_segments", [])
            
            # Apply filters
            if params.status and consultation.get("status") != params.status:
                continue
            
            if params.doctor_name and consultation.get("doctor_name") != params.doctor_name:
                continue
            
            if params.patient_name and patient_info.get("name") != params.patient_name:
                continue
            
            consultation_response = ConsultationStatusResponse(
                consultation_id=consultation_id,
                status=consultation.get("status", "unknown"),
                patient_name=patient_info.get("name", "Unknown"),
                doctor_name=consultation.get("doctor_name"),
                start_time=consultation.get("start_time"),
                duration=consultation.get("duration"),
                transcript_count=len(transcript_segments),
                has_pdf=bool(consultation.get("pdf_file_path"))
            )
            
            consultation_list.append(consultation_response)
        
        # Apply pagination
        start_idx = (params.page - 1) * params.page_size
        end_idx = start_idx + params.page_size
        
        return consultation_list[start_idx:end_idx]
        
    except Exception as e:
        logger.error(f"Error listing consultations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list consultations: {str(e)}"
        )


@router.delete("/{consultation_id}")
async def delete_consultation(consultation_id: str):
    """
    Delete a consultation and clean up associated resources.
    """
    try:
        consultation = get_consultation(consultation_id)
        if not consultation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Consultation {consultation_id} not found"
            )
        
        # Clean up resources
        cleanup_consultation(consultation_id)
        
        logger.info(f"Deleted consultation {consultation_id}")
        
        return {"message": f"Consultation {consultation_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting consultation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete consultation: {str(e)}"
        )
