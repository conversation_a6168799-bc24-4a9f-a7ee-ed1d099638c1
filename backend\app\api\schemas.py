from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from ..models.patient import PatientInfo
from ..models.consultation import ConsultationStatus
from ..models.transcript_segment import SpeakerType


# Request Schemas
class StartConsultationRequest(BaseModel):
    """Request schema for starting a consultation."""
    patient_info: PatientInfo
    doctor_name: Optional[str] = None
    language: str = Field(default="en-US", description="Language code for transcription")


class AudioChunkRequest(BaseModel):
    """Request schema for audio chunk upload."""
    audio_data: str = Field(..., description="Base64 encoded audio data")
    chunk_index: int = Field(..., ge=0, description="Sequential chunk index")
    is_final: bool = Field(default=False, description="Whether this is the final chunk")


class FinishConsultationRequest(BaseModel):
    """Request schema for finishing a consultation."""
    doctor_notes: Optional[str] = None


class GeneratePDFRequest(BaseModel):
    """Request schema for PDF generation."""
    doctor_notes: Optional[str] = None
    include_cover_page: bool = Field(default=True)
    include_transcript: bool = Field(default=True)
    include_notes_page: bool = Field(default=True)


class UpdateTranscriptRequest(BaseModel):
    """Request schema for updating transcript segments."""
    segments: List[Dict[str, Any]]


# Response Schemas
class StartConsultationResponse(BaseModel):
    """Response schema for starting a consultation."""
    consultation_id: str
    status: ConsultationStatus
    message: str = "Consultation started successfully"


class AudioChunkResponse(BaseModel):
    """Response schema for audio chunk processing."""
    consultation_id: str
    chunk_index: int
    transcript: Optional[str] = None
    is_partial: bool = True
    confidence: Optional[float] = None
    processing_time_ms: Optional[float] = None


class FinishConsultationResponse(BaseModel):
    """Response schema for finishing a consultation."""
    consultation_id: str
    status: ConsultationStatus
    transcript_segments: List[Dict[str, Any]]
    summary_stats: Dict[str, Any]
    processing_time_ms: float
    message: str = "Consultation finished successfully"


class GeneratePDFResponse(BaseModel):
    """Response schema for PDF generation."""
    consultation_id: str
    pdf_url: Optional[str] = None
    pdf_filename: str
    file_size_bytes: int
    generation_time_ms: float
    message: str = "PDF generated successfully"


class TranscriptUpdateResponse(BaseModel):
    """Response schema for transcript updates."""
    consultation_id: str
    updated_segments: int
    message: str = "Transcript updated successfully"


class ConsultationStatusResponse(BaseModel):
    """Response schema for consultation status."""
    consultation_id: str
    status: ConsultationStatus
    patient_name: str
    doctor_name: Optional[str]
    start_time: Optional[datetime]
    duration: Optional[float]
    transcript_count: int
    has_pdf: bool


class ErrorResponse(BaseModel):
    """Standard error response schema."""
    error: str
    detail: Optional[str] = None
    consultation_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class HealthCheckResponse(BaseModel):
    """Health check response schema."""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str = "1.0.0"
    services: Dict[str, str] = Field(default_factory=dict)


# Utility Schemas
class PaginationParams(BaseModel):
    """Pagination parameters."""
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)


class ConsultationListParams(PaginationParams):
    """Parameters for listing consultations."""
    status: Optional[ConsultationStatus] = None
    doctor_name: Optional[str] = None
    patient_name: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None


class TranscriptFilterParams(BaseModel):
    """Parameters for filtering transcript segments."""
    speaker: Optional[SpeakerType] = None
    min_confidence: Optional[float] = Field(None, ge=0, le=1)
    start_time: Optional[float] = Field(None, ge=0)
    end_time: Optional[float] = Field(None, ge=0)


# WebSocket Schemas
class WebSocketMessage(BaseModel):
    """Base WebSocket message schema."""
    type: str
    consultation_id: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class AudioStreamMessage(WebSocketMessage):
    """WebSocket message for audio streaming."""
    type: str = "audio_chunk"
    audio_data: str
    chunk_index: int


class TranscriptMessage(WebSocketMessage):
    """WebSocket message for transcript updates."""
    type: str = "transcript_update"
    text: str
    speaker: Optional[SpeakerType] = None
    confidence: Optional[float] = None
    is_partial: bool = True


class StatusMessage(WebSocketMessage):
    """WebSocket message for status updates."""
    type: str = "status_update"
    status: ConsultationStatus
    message: Optional[str] = None


class ErrorMessage(WebSocketMessage):
    """WebSocket message for errors."""
    type: str = "error"
    error: str
    detail: Optional[str] = None
