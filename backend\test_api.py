#!/usr/bin/env python3
"""
Simple test script for DocTranscribe API
"""

import asyncio
import httpx
import json
import base64
from datetime import datetime


async def test_api():
    """Test the DocTranscribe API endpoints."""
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        print("🏥 Testing DocTranscribe API...")
        
        # Test health check
        print("\n1. Testing health check...")
        try:
            response = await client.get(f"{base_url}/health")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.json()}")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test start consultation
        print("\n2. Testing start consultation...")
        consultation_data = {
            "patient_info": {
                "name": "<PERSON>",
                "age": 45,
                "gender": "Male",
                "mrn": "MRN123456",
                "reason_for_visit": "Annual checkup and blood pressure monitoring"
            },
            "doctor_name": "<PERSON><PERSON> <PERSON>",
            "language": "en-US"
        }
        
        try:
            response = await client.post(
                f"{base_url}/api/v1/consultations/start",
                json=consultation_data
            )
            print(f"   Status: {response.status_code}")
            result = response.json()
            print(f"   Response: {result}")
            
            if response.status_code == 200:
                consultation_id = result["consultation_id"]
                print(f"   ✅ Consultation created: {consultation_id}")
                
                # Test audio chunk processing
                print("\n3. Testing audio chunk processing...")
                
                # Create dummy audio data (silence)
                dummy_audio = b'\x00' * 1600  # 100ms of silence at 16kHz
                audio_b64 = base64.b64encode(dummy_audio).decode('utf-8')
                
                audio_chunk_data = {
                    "audio_data": audio_b64,
                    "chunk_index": 0,
                    "is_final": False
                }
                
                try:
                    response = await client.post(
                        f"{base_url}/api/v1/audio/chunk",
                        json=audio_chunk_data,
                        headers={"X-Consultation-ID": consultation_id}
                    )
                    print(f"   Status: {response.status_code}")
                    print(f"   Response: {response.json()}")
                except Exception as e:
                    print(f"   Error: {e}")
                
                # Test consultation status
                print("\n4. Testing consultation status...")
                try:
                    response = await client.get(
                        f"{base_url}/api/v1/consultations/{consultation_id}/status"
                    )
                    print(f"   Status: {response.status_code}")
                    print(f"   Response: {response.json()}")
                except Exception as e:
                    print(f"   Error: {e}")
                
                # Test finish consultation
                print("\n5. Testing finish consultation...")
                finish_data = {
                    "doctor_notes": "Patient appears healthy. Recommend follow-up in 6 months."
                }
                
                try:
                    response = await client.post(
                        f"{base_url}/api/v1/consultations/finish",
                        json=finish_data,
                        headers={"X-Consultation-ID": consultation_id}
                    )
                    print(f"   Status: {response.status_code}")
                    print(f"   Response: {response.json()}")
                except Exception as e:
                    print(f"   Error: {e}")
                
                # Test PDF generation
                print("\n6. Testing PDF generation...")
                pdf_data = {
                    "doctor_notes": "Additional notes for PDF generation.",
                    "include_cover_page": True,
                    "include_transcript": True,
                    "include_notes_page": True
                }
                
                try:
                    response = await client.post(
                        f"{base_url}/api/v1/pdf/generate",
                        json=pdf_data,
                        headers={"X-Consultation-ID": consultation_id}
                    )
                    print(f"   Status: {response.status_code}")
                    print(f"   Response: {response.json()}")
                except Exception as e:
                    print(f"   Error: {e}")
                
                # Test list consultations
                print("\n7. Testing list consultations...")
                try:
                    response = await client.get(f"{base_url}/api/v1/consultations/")
                    print(f"   Status: {response.status_code}")
                    consultations = response.json()
                    print(f"   Found {len(consultations)} consultations")
                    for consultation in consultations:
                        print(f"   - {consultation['patient_name']} ({consultation['status']})")
                except Exception as e:
                    print(f"   Error: {e}")
                
            else:
                print(f"   ❌ Failed to create consultation")
                
        except Exception as e:
            print(f"   Error: {e}")
        
        print("\n✅ API testing completed!")


if __name__ == "__main__":
    print("Starting DocTranscribe API test...")
    print("Make sure the API server is running on http://localhost:8000")
    print("Run: cd backend && python -m uvicorn app.main:app --reload")
    print()
    
    asyncio.run(test_api())
