import asyncio
import logging
import json
from typing import AsyncGenerator, Optional, Dict, Any
import httpx
from ..core.config import settings

logger = logging.getLogger(__name__)


class RivaClient:
    """NVIDIA Riva ASR client using HTTP API."""
    
    def __init__(self):
        self.api_key = settings.nvidia_api_key
        self.function_id = settings.riva_function_id
        self.base_url = settings.riva_base_url
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def transcribe_audio_stream(
        self, 
        audio_chunks: AsyncGenerator[bytes, None],
        language: str = "en-US",
        sample_rate: int = 16000
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Transcribe audio stream using NVIDIA Riva API.
        
        Args:
            audio_chunks: Async generator of audio chunks
            language: Language code (e.g., "en-US", "es-ES", "hi-IN")
            sample_rate: Audio sample rate
            
        Yields:
            Transcription results with partial and final hypotheses
        """
        try:
            # Prepare the request payload for streaming ASR
            payload = {
                "model": self.function_id,
                "stream": True,
                "language": language,
                "response_format": "json",
                "temperature": 0.0,
                "max_tokens": 1024
            }
            
            # For streaming, we'll need to handle audio chunks differently
            # This is a simplified implementation - in practice, you'd need
            # to implement proper streaming with the NVIDIA API
            
            async for chunk in audio_chunks:
                if chunk:
                    # Convert audio chunk to base64 for API
                    import base64
                    audio_b64 = base64.b64encode(chunk).decode('utf-8')
                    
                    # Send chunk to NVIDIA API
                    response = await self._send_audio_chunk(audio_b64, payload)
                    if response:
                        yield response
                        
        except Exception as e:
            logger.error(f"Error in audio transcription: {e}")
            yield {
                "error": str(e),
                "is_final": True,
                "alternatives": []
            }
    
    async def _send_audio_chunk(self, audio_b64: str, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Send audio chunk to NVIDIA API and get transcription."""
        try:
            # Add audio data to payload
            request_payload = {
                **payload,
                "input": audio_b64
            }
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/audio/transcriptions",
                headers=self.headers,
                json=request_payload
            ) as response:
                if response.status_code == 200:
                    async for line in response.aiter_lines():
                        if line.strip():
                            try:
                                data = json.loads(line)
                                return self._format_response(data)
                            except json.JSONDecodeError:
                                continue
                else:
                    logger.error(f"API error: {response.status_code} - {await response.aread()}")
                    
        except Exception as e:
            logger.error(f"Error sending audio chunk: {e}")
            return None
    
    def _format_response(self, api_response: Dict[str, Any]) -> Dict[str, Any]:
        """Format NVIDIA API response to our standard format."""
        try:
            # Extract text from API response
            text = api_response.get("text", "")
            
            return {
                "alternatives": [
                    {
                        "transcript": text,
                        "confidence": 0.9  # NVIDIA API might not provide confidence
                    }
                ],
                "is_final": True,  # Assume final for now
                "stability": 0.9,
                "result_end_time": 0.0  # Would need to track timing
            }
        except Exception as e:
            logger.error(f"Error formatting response: {e}")
            return {
                "alternatives": [],
                "is_final": True,
                "error": str(e)
            }
    
    async def transcribe_file(
        self, 
        audio_file_path: str, 
        language: str = "en-US"
    ) -> Dict[str, Any]:
        """
        Transcribe a complete audio file.
        
        Args:
            audio_file_path: Path to audio file
            language: Language code
            
        Returns:
            Complete transcription result
        """
        try:
            # Read audio file
            with open(audio_file_path, "rb") as f:
                audio_data = f.read()
            
            # Convert to base64
            import base64
            audio_b64 = base64.b64encode(audio_data).decode('utf-8')
            
            payload = {
                "model": self.function_id,
                "input": audio_b64,
                "language": language,
                "response_format": "json",
                "temperature": 0.0
            }
            
            response = await self.client.post(
                f"{self.base_url}/audio/transcriptions",
                headers=self.headers,
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                return {
                    "text": result.get("text", ""),
                    "language": language,
                    "duration": 0.0,  # Would need to calculate
                    "segments": []  # Would need to parse segments
                }
            else:
                logger.error(f"File transcription error: {response.status_code}")
                return {"error": f"API error: {response.status_code}"}
                
        except Exception as e:
            logger.error(f"Error transcribing file: {e}")
            return {"error": str(e)}
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


# Global client instance
riva_client = RivaClient()
