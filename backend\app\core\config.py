import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    # NVIDIA Riva Configuration
    nvidia_api_key: str = Field(..., env="NVIDIA_API_KEY")
    riva_function_id: str = Field(default="canary-1b-asr", env="RIVA_FUNCTION_ID")
    riva_base_url: str = Field(default="https://integrate.api.nvidia.com/v1", env="RIVA_BASE_URL")
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    debug: bool = Field(default=True, env="DEBUG")
    
    # Security
    jwt_secret_key: str = Field(..., env="JWT_SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Database
    database_url: str = Field(default="sqlite:///./doctranscribe.db", env="DATABASE_URL")
    
    # Audio Processing
    max_audio_chunk_size: int = Field(default=1048576, env="MAX_AUDIO_CHUNK_SIZE")  # 1MB
    audio_sample_rate: int = Field(default=16000, env="AUDIO_SAMPLE_RATE")
    audio_channels: int = Field(default=1, env="AUDIO_CHANNELS")
    
    # PDF Configuration
    pdf_output_dir: str = Field(default="./pdfs", env="PDF_OUTPUT_DIR")
    temp_audio_dir: str = Field(default="./temp_audio", env="TEMP_AUDIO_DIR")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()

# Ensure directories exist
os.makedirs(settings.pdf_output_dir, exist_ok=True)
os.makedirs(settings.temp_audio_dir, exist_ok=True)
