from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum


class SpeakerType(str, Enum):
    """Enumeration for speaker types."""
    DOCTOR = "doctor"
    PATIENT = "patient"
    OTHER = "other"
    UNKNOWN = "unknown"


class TranscriptSegment(BaseModel):
    """Individual transcript segment model."""
    id: Optional[str] = None
    consultation_id: str = Field(..., description="Associated consultation ID")
    start_time: float = Field(..., ge=0, description="Start time in seconds")
    end_time: float = Field(..., ge=0, description="End time in seconds")
    speaker: SpeakerType = Field(..., description="Speaker identification")
    text: str = Field(..., min_length=1, description="Transcribed text")
    confidence: Optional[float] = Field(None, ge=0, le=1, description="Transcription confidence score")
    is_final: bool = Field(default=True, description="Whether this is a final transcript")
    
    class Config:
        json_schema_extra = {
            "example": {
                "consultation_id": "123e4567-e89b-12d3-a456-426614174000",
                "start_time": 10.5,
                "end_time": 15.2,
                "speaker": "doctor",
                "text": "How are you feeling today?",
                "confidence": 0.95,
                "is_final": True
            }
        }


class TranscriptSegmentCreate(BaseModel):
    """Model for creating transcript segments."""
    consultation_id: str
    start_time: float
    end_time: float
    speaker: SpeakerType
    text: str
    confidence: Optional[float] = None
    is_final: bool = True


class TranscriptSegmentUpdate(BaseModel):
    """Model for updating transcript segments."""
    text: Optional[str] = None
    speaker: Optional[SpeakerType] = None
    confidence: Optional[float] = None


class TranscriptResponse(BaseModel):
    """Response model for transcript data."""
    segments: List[TranscriptSegment]
    total_duration: float
    word_count: int
    speaker_stats: dict
    created_at: datetime
    
    class Config:
        json_schema_extra = {
            "example": {
                "segments": [
                    {
                        "consultation_id": "123e4567-e89b-12d3-a456-426614174000",
                        "start_time": 0.0,
                        "end_time": 3.5,
                        "speaker": "doctor",
                        "text": "Good morning, how can I help you today?",
                        "confidence": 0.98,
                        "is_final": True
                    }
                ],
                "total_duration": 300.5,
                "word_count": 150,
                "speaker_stats": {
                    "doctor": {"duration": 180.2, "word_count": 90},
                    "patient": {"duration": 120.3, "word_count": 60}
                },
                "created_at": "2024-01-15T10:30:00Z"
            }
        }
