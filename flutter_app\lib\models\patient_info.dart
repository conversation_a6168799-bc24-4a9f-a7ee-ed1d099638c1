/// Patient information model
class PatientInfo {
  final String name;
  final int age;
  final String gender;
  final String? mrn;
  final String reasonForVisit;
  final DateTime? dateOfBirth;
  final String? phoneNumber;
  final String? email;
  final String? address;
  final String? emergencyContact;

  const PatientInfo({
    required this.name,
    required this.age,
    required this.gender,
    this.mrn,
    required this.reasonForVisit,
    this.dateOfBirth,
    this.phoneNumber,
    this.email,
    this.address,
    this.emergencyContact,
  });

  /// Create PatientInfo from JSON
  factory PatientInfo.fromJson(Map<String, dynamic> json) {
    return PatientInfo(
      name: json['name'] as String,
      age: json['age'] as int,
      gender: json['gender'] as String,
      mrn: json['mrn'] as String?,
      reasonForVisit: json['reason_for_visit'] as String,
      dateOfBirth: json['date_of_birth'] != null
          ? DateTime.parse(json['date_of_birth'] as String)
          : null,
      phoneNumber: json['phone_number'] as String?,
      email: json['email'] as String?,
      address: json['address'] as String?,
      emergencyContact: json['emergency_contact'] as String?,
    );
  }

  /// Convert PatientInfo to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'age': age,
      'gender': gender,
      'mrn': mrn,
      'reason_for_visit': reasonForVisit,
      'date_of_birth': dateOfBirth?.toIso8601String(),
      'phone_number': phoneNumber,
      'email': email,
      'address': address,
      'emergency_contact': emergencyContact,
    };
  }

  /// Create a copy with updated fields
  PatientInfo copyWith({
    String? name,
    int? age,
    String? gender,
    String? mrn,
    String? reasonForVisit,
    DateTime? dateOfBirth,
    String? phoneNumber,
    String? email,
    String? address,
    String? emergencyContact,
  }) {
    return PatientInfo(
      name: name ?? this.name,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      mrn: mrn ?? this.mrn,
      reasonForVisit: reasonForVisit ?? this.reasonForVisit,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      address: address ?? this.address,
      emergencyContact: emergencyContact ?? this.emergencyContact,
    );
  }

  /// Validate patient information
  List<String> validate() {
    final errors = <String>[];

    if (name.trim().isEmpty) {
      errors.add('Patient name is required');
    } else if (name.length > 100) {
      errors.add('Patient name must be less than 100 characters');
    }

    if (age < 0 || age > 150) {
      errors.add('Age must be between 0 and 150');
    }

    if (gender.trim().isEmpty) {
      errors.add('Gender is required');
    }

    if (reasonForVisit.trim().isEmpty) {
      errors.add('Reason for visit is required');
    } else if (reasonForVisit.length > 500) {
      errors.add('Reason for visit must be less than 500 characters');
    }

    if (mrn != null && mrn!.isNotEmpty && mrn!.length > 50) {
      errors.add('MRN must be less than 50 characters');
    }

    return errors;
  }

  /// Check if patient info is valid
  bool get isValid => validate().isEmpty;

  @override
  String toString() {
    return 'PatientInfo(name: $name, age: $age, gender: $gender, mrn: $mrn, reasonForVisit: $reasonForVisit)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is PatientInfo &&
        other.name == name &&
        other.age == age &&
        other.gender == gender &&
        other.mrn == mrn &&
        other.reasonForVisit == reasonForVisit &&
        other.dateOfBirth == dateOfBirth &&
        other.phoneNumber == phoneNumber &&
        other.email == email &&
        other.address == address &&
        other.emergencyContact == emergencyContact;
  }

  @override
  int get hashCode {
    return Object.hash(
      name,
      age,
      gender,
      mrn,
      reasonForVisit,
      dateOfBirth,
      phoneNumber,
      email,
      address,
      emergencyContact,
    );
  }
}

/// Gender options
enum Gender {
  male('Male'),
  female('Female'),
  other('Other'),
  preferNotToSay('Prefer not to say');

  const Gender(this.displayName);
  final String displayName;

  static Gender fromString(String value) {
    switch (value.toLowerCase()) {
      case 'male':
        return Gender.male;
      case 'female':
        return Gender.female;
      case 'other':
        return Gender.other;
      case 'prefer not to say':
        return Gender.preferNotToSay;
      default:
        return Gender.other;
    }
  }
}
